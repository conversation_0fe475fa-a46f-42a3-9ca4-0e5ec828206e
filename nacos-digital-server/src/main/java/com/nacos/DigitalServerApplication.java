package com.nacos;

import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import com.nacos.config.UniversalTaskConfig;

@Slf4j
@EnableTransactionManagement
@EnableAsync
@EnableScheduling
@SpringBootApplication
@ComponentScan(basePackages = {"com.nacos", "com.business"})
@MapperScan("com.nacos.mapper")
@EnableConfigurationProperties({UniversalTaskConfig.class})
public class DigitalServerApplication {
    public static void main(String[] args) {
        SpringApplication.run(DigitalServerApplication.class, args);
        log.info("(♥◠‿◠)ﾉﾞ  Digital启动成功 - 统一任务系统已加载   ლ(´ڡ`ლ)ﾞ");
        log.info("统一任务系统架构: 策略模式 + 工厂模式 + Redis消息队列");
        log.info("支持任务类型: 视频翻译、音频生成、视频编辑");
    }

}
