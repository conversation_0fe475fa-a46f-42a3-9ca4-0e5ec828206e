package com.nacos.config.message.mq;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.nacos.entity.dto.TaskMessage;
import com.nacos.entity.dto.TaskProcessResult;
import com.nacos.entity.enums.TaskType;
import com.nacos.service.scheduler.UniversalTaskScheduler;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.stereotype.Component;

/**
 * 统一任务消息监听器
 * 
 * 负责处理来自Redis队列的任务消息，包括：
 * 1. 消息解析和验证
 * 2. 任务类型识别和路由
 * 3. 调用统一任务调度器处理
 * 4. 异常处理和错误恢复
 * 
 * 设计原则：
 * 1. 统一入口：所有队列消息都通过此监听器处理
 * 2. 类型安全：严格的消息格式验证和类型检查
 * 3. 异常隔离：单个消息处理失败不影响其他消息
 * 4. 可观测性：完整的日志记录和监控支持
 * 
 * <AUTHOR>
 * @since 2025-07-31
 * @version 1.0 - 统一任务消息监听架构
 */
@Component
@Slf4j
public class UniversalTaskMessageListener implements MessageListener {

    @Autowired
    private UniversalTaskScheduler taskScheduler;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * Redis消息监听器接口实现
     * 
     * @param message Redis消息对象
     * @param pattern 匹配的主题模式
     */
    @Override
    public void onMessage(@NotNull Message message, byte[] pattern) {
        String topicName = new String(pattern);
        String messageBody = new String(message.getBody());
        
        log.info("Received task message from topic: {}, message: {}", topicName, messageBody);
        
        try {
            // 1. 识别任务类型
            TaskType taskType = TaskType.fromRedisTopic(topicName);
            if (taskType == null) {
                log.error("Unknown task type for topic: {}", topicName);
                return;
            }
            
            // 2. 解析消息内容
            TaskMessage taskMessage = parseTaskMessage(messageBody, taskType);
            if (taskMessage == null) {
                log.error("Failed to parse task message: topic={}, message={}", topicName, messageBody);
                return;
            }
            
            // 3. 验证消息完整性
            if (!validateTaskMessage(taskMessage)) {
                log.error("Invalid task message: topic={}, taskId={}", topicName, taskMessage.getTaskId());
                return;
            }
            
            // 4. 调用任务调度器处理
            TaskProcessResult result = taskScheduler.processTaskMessage(taskMessage);
            
            // 5. 记录处理结果
            if (result.isSuccess()) {
                log.info("Task message processed successfully: topic={}, taskId={}, message={}", 
                        topicName, taskMessage.getTaskId(), result.getMessage());
            } else {
                log.warn("Task message processing failed: topic={}, taskId={}, error={}", 
                        topicName, taskMessage.getTaskId(), result.getMessage());
            }
            
        } catch (Exception e) {
            log.error("Failed to process task message: topic={}, message={}", topicName, messageBody, e);
            // 消息处理失败不抛出异常，避免影响其他消息处理
        }
    }

    /**
     * 解析任务消息
     * 
     * @param messageBody 消息体JSON字符串
     * @param taskType    任务类型
     * @return 解析后的任务消息对象，解析失败返回null
     */
    private TaskMessage parseTaskMessage(String messageBody, TaskType taskType) {
        try {
            // 尝试解析为TaskMessage对象
            TaskMessage taskMessage = objectMapper.readValue(messageBody, TaskMessage.class);
            
            // 设置任务类型（如果消息中没有指定）
            if (taskMessage.getTaskType() == null) {
                taskMessage.setTaskType(taskType);
            }
            
            return taskMessage;
            
        } catch (Exception e) {
            log.error("Failed to parse task message JSON: taskType={}, message={}", taskType, messageBody, e);
            return null;
        }
    }

    /**
     * 验证任务消息完整性
     * 
     * @param taskMessage 任务消息对象
     * @return 验证是否通过
     */
    private boolean validateTaskMessage(TaskMessage taskMessage) {
        // 检查必要字段
        if (taskMessage.getTaskId() == null || taskMessage.getTaskId().trim().isEmpty()) {
            log.error("Task message missing taskId");
            return false;
        }
        
        if (taskMessage.getTaskType() == null) {
            log.error("Task message missing taskType: taskId={}", taskMessage.getTaskId());
            return false;
        }
        
        if (taskMessage.getUserId() == null || taskMessage.getUserId().trim().isEmpty()) {
            log.error("Task message missing userId: taskId={}", taskMessage.getTaskId());
            return false;
        }
        
        // 检查任务属性
        if (taskMessage.getAttributes() == null || taskMessage.getAttributes().isEmpty()) {
            log.warn("Task message has empty attributes: taskId={}", taskMessage.getTaskId());
            // 属性为空不算错误，某些任务可能不需要额外属性
        }
        
        return true;
    }
}
