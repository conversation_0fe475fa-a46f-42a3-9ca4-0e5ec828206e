package com.nacos.entity.context;

import com.nacos.entity.enums.TaskType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 任务上下文对象
 * 
 * 用于在任务处理过程中传递所有必要的信息，包括：
 * 1. 基础任务信息（ID、用户、类型等）
 * 2. 强类型业务参数
 * 3. 扩展元数据
 * 4. 执行上下文信息
 * 
 * 设计原则：
 * 1. 使用泛型支持不同任务类型的强类型参数
 * 2. 包含足够的上下文信息，减少处理器对外部依赖
 * 3. 支持扩展字段，便于未来功能扩展
 * 4. 线程安全，支持并发处理
 * 
 * @param <T> 业务参数类型
 * <AUTHOR>
 * @since 2025-07-31
 * @version 1.0 - 统一任务上下文架构
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(description = "任务上下文对象")
public class TaskContext<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务唯一标识
     */
    @Schema(description = "任务唯一标识", required = true)
    private String taskId;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID", required = true)
    private String userId;

    /**
     * 任务类型
     */
    @Schema(description = "任务类型", required = true)
    private TaskType taskType;

    /**
     * 强类型业务参数
     * 不同任务类型对应不同的参数对象
     */
    @Schema(description = "强类型业务参数")
    private T businessParams;

    /**
     * 任务名称
     */
    @Schema(description = "任务名称")
    private String taskName;

    /**
     * 任务描述
     */
    @Schema(description = "任务描述")
    private String taskDescription;

    /**
     * 服务商标识
     */
    @Schema(description = "服务商标识", example = "AZURE, MINIMAX")
    private String provider;

    /**
     * 任务优先级
     * 数值越小优先级越高
     */
    @Schema(description = "任务优先级，数值越小优先级越高")
    @Builder.Default
    private Integer priority = 5;

    /**
     * 超时时间（分钟）
     */
    @Schema(description = "超时时间（分钟）")
    private Integer timeoutMinutes;

    /**
     * 任务创建时间
     */
    @Schema(description = "任务创建时间")
    private LocalDateTime createdTime;

    /**
     * 任务开始处理时间
     */
    @Schema(description = "任务开始处理时间")
    private LocalDateTime startTime;

    /**
     * 重试次数
     */
    @Schema(description = "重试次数")
    @Builder.Default
    private Integer retryCount = 0;

    /**
     * 最大重试次数
     */
    @Schema(description = "最大重试次数")
    @Builder.Default
    private Integer maxRetryCount = 3;

    /**
     * 扩展元数据
     * 用于存储任务处理过程中的额外信息
     */
    @Schema(description = "扩展元数据")
    @Builder.Default
    private Map<String, Object> metadata = new HashMap<>();

    /**
     * 执行上下文信息
     * 用于存储执行过程中的临时数据
     */
    @Schema(description = "执行上下文信息")
    @Builder.Default
    private Map<String, Object> executionContext = new HashMap<>();

    // ==================== 便捷方法 ====================

    /**
     * 添加元数据
     * 
     * @param key   键
     * @param value 值
     * @return 当前对象，支持链式调用
     */
    public TaskContext<T> addMetadata(String key, Object value) {
        if (this.metadata == null) {
            this.metadata = new HashMap<>();
        }
        this.metadata.put(key, value);
        return this;
    }

    /**
     * 获取元数据
     * 
     * @param key 键
     * @return 值
     */
    public Object getMetadata(String key) {
        return this.metadata != null ? this.metadata.get(key) : null;
    }

    /**
     * 获取元数据（指定类型）
     * 
     * @param key   键
     * @param clazz 值类型
     * @param <V>   值类型泛型
     * @return 值
     */
    @SuppressWarnings("unchecked")
    public <V> V getMetadata(String key, Class<V> clazz) {
        Object value = getMetadata(key);
        if (value != null && clazz.isInstance(value)) {
            return (V) value;
        }
        return null;
    }

    /**
     * 添加执行上下文信息
     * 
     * @param key   键
     * @param value 值
     * @return 当前对象，支持链式调用
     */
    public TaskContext<T> addExecutionContext(String key, Object value) {
        if (this.executionContext == null) {
            this.executionContext = new HashMap<>();
        }
        this.executionContext.put(key, value);
        return this;
    }

    /**
     * 获取执行上下文信息
     * 
     * @param key 键
     * @return 值
     */
    public Object getExecutionContext(String key) {
        return this.executionContext != null ? this.executionContext.get(key) : null;
    }

    /**
     * 获取执行上下文信息（指定类型）
     * 
     * @param key   键
     * @param clazz 值类型
     * @param <V>   值类型泛型
     * @return 值
     */
    @SuppressWarnings("unchecked")
    public <V> V getExecutionContext(String key, Class<V> clazz) {
        Object value = getExecutionContext(key);
        if (value != null && clazz.isInstance(value)) {
            return (V) value;
        }
        return null;
    }

    /**
     * 检查是否可以重试
     * 
     * @return 是否可以重试
     */
    public boolean canRetry() {
        return this.retryCount < this.maxRetryCount;
    }

    /**
     * 增加重试次数
     * 
     * @return 当前重试次数
     */
    public int incrementRetryCount() {
        this.retryCount++;
        return this.retryCount;
    }

    /**
     * 检查是否超时
     * 
     * @return 是否超时
     */
    public boolean isTimeout() {
        if (this.startTime == null || this.timeoutMinutes == null) {
            return false;
        }
        
        LocalDateTime timeoutTime = this.startTime.plusMinutes(this.timeoutMinutes);
        return LocalDateTime.now().isAfter(timeoutTime);
    }

    /**
     * 获取已执行时间（分钟）
     * 
     * @return 已执行时间，如果未开始返回0
     */
    public long getExecutedMinutes() {
        if (this.startTime == null) {
            return 0;
        }
        
        return java.time.Duration.between(this.startTime, LocalDateTime.now()).toMinutes();
    }

    /**
     * 获取剩余超时时间（分钟）
     * 
     * @return 剩余超时时间，如果未设置超时或未开始返回-1
     */
    public long getRemainingTimeoutMinutes() {
        if (this.startTime == null || this.timeoutMinutes == null) {
            return -1;
        }
        
        long executedMinutes = getExecutedMinutes();
        return Math.max(0, this.timeoutMinutes - executedMinutes);
    }

    /**
     * 创建任务上下文的副本（深拷贝元数据）
     * 
     * @return 任务上下文副本
     */
    public TaskContext<T> copy() {
        return TaskContext.<T>builder()
                .taskId(this.taskId)
                .userId(this.userId)
                .taskType(this.taskType)
                .businessParams(this.businessParams)
                .taskName(this.taskName)
                .taskDescription(this.taskDescription)
                .provider(this.provider)
                .priority(this.priority)
                .timeoutMinutes(this.timeoutMinutes)
                .createdTime(this.createdTime)
                .startTime(this.startTime)
                .retryCount(this.retryCount)
                .maxRetryCount(this.maxRetryCount)
                .metadata(this.metadata != null ? new HashMap<>(this.metadata) : new HashMap<>())
                .executionContext(this.executionContext != null ? new HashMap<>(this.executionContext) : new HashMap<>())
                .build();
    }

    @Override
    public String toString() {
        return String.format("TaskContext{taskId='%s', userId='%s', taskType=%s, provider='%s', priority=%d}", 
                           taskId, userId, taskType, provider, priority);
    }
}
