package com.nacos.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 回调数据对象
 * 
 * 用于封装外部API的异步回调数据，包括：
 * 1. 外部任务状态和结果信息
 * 2. 结果文件URL和元数据
 * 3. 错误信息和处理建议
 * 4. 回调时间和来源信息
 * 
 * 设计原则：
 * 1. 通用的回调数据格式，适配不同的外部API
 * 2. 包含足够的信息用于任务状态更新
 * 3. 支持结构化的错误信息处理
 * 4. 便于回调数据的验证和审计
 * 
 * <AUTHOR>
 * @since 2025-07-31
 * @version 1.0 - 统一回调数据架构
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(description = "回调数据对象")
public class CallbackData implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 外部API任务ID
     */
    @Schema(description = "外部API任务ID", required = true)
    private String externalJobId;

    /**
     * 回调状态
     * 外部API返回的原始状态值
     */
    @Schema(description = "回调状态")
    private String status;

    /**
     * 状态描述
     */
    @Schema(description = "状态描述")
    private String statusDescription;

    /**
     * 是否成功
     */
    @Schema(description = "是否成功")
    private Boolean success;

    /**
     * 结果文件URL
     * 主要结果文件的下载地址
     */
    @Schema(description = "结果文件URL")
    private String resultUrl;

    /**
     * 结果文件类型
     */
    @Schema(description = "结果文件类型", example = "video/mp4, audio/mp3")
    private String resultFileType;

    /**
     * 结果文件大小（字节）
     */
    @Schema(description = "结果文件大小（字节）")
    private Long resultFileSize;

    /**
     * 结果文件时长（毫秒）
     * 适用于音视频文件
     */
    @Schema(description = "结果文件时长（毫秒）")
    private Long resultDurationMs;

    /**
     * 缩略图URL
     */
    @Schema(description = "缩略图URL")
    private String thumbnailUrl;

    /**
     * 封面图URL
     */
    @Schema(description = "封面图URL")
    private String coverUrl;

    /**
     * 字幕文件URL
     */
    @Schema(description = "字幕文件URL")
    private String subtitleUrl;

    /**
     * 错误消息
     */
    @Schema(description = "错误消息")
    private String errorMessage;

    /**
     * 错误代码
     */
    @Schema(description = "错误代码")
    private String errorCode;

    /**
     * 错误详情
     */
    @Schema(description = "错误详情")
    private String errorDetail;

    /**
     * 处理进度（0-100）
     */
    @Schema(description = "处理进度（0-100）")
    private Integer progress;

    /**
     * 预计剩余时间（分钟）
     */
    @Schema(description = "预计剩余时间（分钟）")
    private Integer estimatedRemainingMinutes;

    /**
     * 回调时间
     */
    @Schema(description = "回调时间")
    private LocalDateTime callbackTime;

    /**
     * 回调来源
     */
    @Schema(description = "回调来源", example = "WEBHOOK, POLLING")
    private String callbackSource;

    /**
     * 回调原始数据
     * 保存外部API返回的完整原始数据
     */
    @Schema(description = "回调原始数据")
    private String rawData;

    /**
     * 服务商标识
     */
    @Schema(description = "服务商标识")
    private String provider;

    /**
     * API版本
     */
    @Schema(description = "API版本")
    private String apiVersion;

    /**
     * 费用信息
     */
    @Schema(description = "费用信息")
    private Double cost;

    /**
     * 费用单位
     */
    @Schema(description = "费用单位", example = "CNY, USD")
    private String costUnit;

    /**
     * 质量评分（0-100）
     */
    @Schema(description = "质量评分（0-100）")
    private Integer qualityScore;

    /**
     * 附加数据
     * 存储特定服务商的扩展信息
     */
    @Schema(description = "附加数据")
    @Builder.Default
    private Map<String, Object> additionalData = new HashMap<>();

    /**
     * 元数据
     */
    @Schema(description = "元数据")
    @Builder.Default
    private Map<String, Object> metadata = new HashMap<>();

    // ==================== 静态工厂方法 ====================

    /**
     * 创建成功回调数据
     * 
     * @param externalJobId 外部任务ID
     * @param resultUrl     结果URL
     * @return 成功回调数据
     */
    public static CallbackData success(String externalJobId, String resultUrl) {
        return CallbackData.builder()
                .externalJobId(externalJobId)
                .success(true)
                .status("SUCCESS")
                .statusDescription("处理成功")
                .resultUrl(resultUrl)
                .callbackTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建失败回调数据
     * 
     * @param externalJobId 外部任务ID
     * @param errorMessage  错误消息
     * @return 失败回调数据
     */
    public static CallbackData failure(String externalJobId, String errorMessage) {
        return CallbackData.builder()
                .externalJobId(externalJobId)
                .success(false)
                .status("FAILED")
                .statusDescription("处理失败")
                .errorMessage(errorMessage)
                .callbackTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建进行中回调数据
     * 
     * @param externalJobId 外部任务ID
     * @param progress      进度
     * @return 进行中回调数据
     */
    public static CallbackData processing(String externalJobId, int progress) {
        return CallbackData.builder()
                .externalJobId(externalJobId)
                .success(null) // 进行中状态，成功状态未确定
                .status("PROCESSING")
                .statusDescription("正在处理中")
                .progress(progress)
                .callbackTime(LocalDateTime.now())
                .build();
    }

    // ==================== 便捷方法 ====================

    /**
     * 添加附加数据
     * 
     * @param key   键
     * @param value 值
     * @return 当前对象，支持链式调用
     */
    public CallbackData addAdditionalData(String key, Object value) {
        if (this.additionalData == null) {
            this.additionalData = new HashMap<>();
        }
        this.additionalData.put(key, value);
        return this;
    }

    /**
     * 获取附加数据
     * 
     * @param key 键
     * @return 值
     */
    public Object getAdditionalData(String key) {
        return this.additionalData != null ? this.additionalData.get(key) : null;
    }

    /**
     * 获取附加数据（指定类型）
     * 
     * @param key   键
     * @param clazz 值类型
     * @param <T>   值类型泛型
     * @return 值
     */
    @SuppressWarnings("unchecked")
    public <T> T getAdditionalData(String key, Class<T> clazz) {
        Object value = getAdditionalData(key);
        if (value != null && clazz.isInstance(value)) {
            return (T) value;
        }
        return null;
    }

    /**
     * 添加元数据
     * 
     * @param key   键
     * @param value 值
     * @return 当前对象，支持链式调用
     */
    public CallbackData addMetadata(String key, Object value) {
        if (this.metadata == null) {
            this.metadata = new HashMap<>();
        }
        this.metadata.put(key, value);
        return this;
    }

    /**
     * 检查是否为最终状态
     * 
     * @return 是否为最终状态
     */
    public boolean isFinalStatus() {
        if (this.success != null) {
            return true; // 明确的成功或失败状态
        }
        
        // 根据状态字符串判断
        if (this.status != null) {
            String upperStatus = this.status.toUpperCase();
            return upperStatus.contains("SUCCESS") || 
                   upperStatus.contains("FAILED") || 
                   upperStatus.contains("ERROR") || 
                   upperStatus.contains("COMPLETED") ||
                   upperStatus.contains("CANCELLED") ||
                   upperStatus.contains("TIMEOUT");
        }
        
        return false;
    }

    /**
     * 检查是否有结果文件
     * 
     * @return 是否有结果文件
     */
    public boolean hasResultFile() {
        return this.resultUrl != null && !this.resultUrl.trim().isEmpty();
    }

    /**
     * 检查是否有错误信息
     * 
     * @return 是否有错误信息
     */
    public boolean hasError() {
        return (this.errorMessage != null && !this.errorMessage.trim().isEmpty()) ||
               (this.errorCode != null && !this.errorCode.trim().isEmpty());
    }

    /**
     * 获取格式化的错误信息
     * 
     * @return 格式化的错误信息
     */
    public String getFormattedError() {
        StringBuilder error = new StringBuilder();
        
        if (this.errorCode != null && !this.errorCode.trim().isEmpty()) {
            error.append("[").append(this.errorCode).append("] ");
        }
        
        if (this.errorMessage != null && !this.errorMessage.trim().isEmpty()) {
            error.append(this.errorMessage);
        }
        
        if (this.errorDetail != null && !this.errorDetail.trim().isEmpty()) {
            if (error.length() > 0) {
                error.append(" - ");
            }
            error.append(this.errorDetail);
        }
        
        return error.toString();
    }

    @Override
    public String toString() {
        return String.format("CallbackData{externalJobId='%s', status='%s', success=%s, resultUrl='%s'}", 
                           externalJobId, status, success, resultUrl);
    }
}
