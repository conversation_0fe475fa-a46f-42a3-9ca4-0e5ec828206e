package com.nacos.entity.dto;

import com.nacos.entity.enums.TaskType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 任务消息对象
 * 
 * 用于Redis消息队列中传递的任务信息，包括：
 * 1. 基础任务标识信息
 * 2. 任务优先级和调度信息
 * 3. 重试和超时控制
 * 4. 扩展元数据
 * 
 * 设计原则：
 * 1. 轻量级设计，只包含调度必需的信息
 * 2. 支持任务优先级和延迟调度
 * 3. 包含重试控制和故障恢复信息
 * 4. 便于序列化和网络传输
 * 
 * <AUTHOR>
 * @since 2025-07-31
 * @version 1.0 - 统一任务消息架构
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(description = "任务消息对象")
public class TaskMessage implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务唯一标识
     */
    @Schema(description = "任务唯一标识", required = true)
    private String taskId;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID", required = true)
    private String userId;

    /**
     * 任务类型
     */
    @Schema(description = "任务类型", required = true)
    private TaskType taskType;

    /**
     * 任务优先级
     * 数值越小优先级越高，默认为5
     */
    @Schema(description = "任务优先级，数值越小优先级越高")
    @Builder.Default
    private Integer priority = 5;

    /**
     * 消息发送时间
     */
    @Schema(description = "消息发送时间")
    @Builder.Default
    private LocalDateTime sendTime = LocalDateTime.now();

    /**
     * 计划执行时间
     * 用于延迟调度，如果为null则立即执行
     */
    @Schema(description = "计划执行时间")
    private LocalDateTime scheduledTime;

    /**
     * 超时时间（分钟）
     */
    @Schema(description = "超时时间（分钟）")
    private Integer timeoutMinutes;

    /**
     * 当前重试次数
     */
    @Schema(description = "当前重试次数")
    @Builder.Default
    private Integer retryCount = 0;

    /**
     * 最大重试次数
     */
    @Schema(description = "最大重试次数")
    @Builder.Default
    private Integer maxRetryCount = 3;

    /**
     * 重试延迟（秒）
     */
    @Schema(description = "重试延迟（秒）")
    private Integer retryDelaySeconds;

    /**
     * 消息来源
     */
    @Schema(description = "消息来源", example = "USER_SUBMIT, RETRY, FALLBACK")
    private String source;

    /**
     * 消息版本
     * 用于消息格式兼容性控制
     */
    @Schema(description = "消息版本")
    @Builder.Default
    private String version = "1.0";

    /**
     * 消息ID
     * 用于消息去重和追踪
     */
    @Schema(description = "消息ID")
    private String messageId;

    /**
     * 父消息ID
     * 用于重试消息的关联
     */
    @Schema(description = "父消息ID")
    private String parentMessageId;

    /**
     * 消息标签
     * 用于消息分类和过滤
     */
    @Schema(description = "消息标签")
    private String tags;

    /**
     * 扩展属性
     * 存储额外的调度信息
     */
    @Schema(description = "扩展属性")
    @Builder.Default
    private Map<String, Object> attributes = new HashMap<>();

    /**
     * 调度上下文
     * 存储调度过程中的临时信息
     */
    @Schema(description = "调度上下文")
    @Builder.Default
    private Map<String, Object> schedulingContext = new HashMap<>();

    // ==================== 静态工厂方法 ====================

    /**
     * 创建用户提交的任务消息
     * 
     * @param taskId   任务ID
     * @param userId   用户ID
     * @param taskType 任务类型
     * @return 任务消息
     */
    public static TaskMessage userSubmit(String taskId, String userId, TaskType taskType) {
        return TaskMessage.builder()
                .taskId(taskId)
                .userId(userId)
                .taskType(taskType)
                .source("USER_SUBMIT")
                .messageId(generateMessageId())
                .priority(taskType.getPriority())
                .timeoutMinutes(taskType.getDefaultTimeoutMinutes())
                .build();
    }

    /**
     * 创建重试任务消息
     * 
     * @param originalMessage 原始消息
     * @param retryDelaySeconds 重试延迟
     * @return 重试消息
     */
    public static TaskMessage retry(TaskMessage originalMessage, int retryDelaySeconds) {
        return TaskMessage.builder()
                .taskId(originalMessage.getTaskId())
                .userId(originalMessage.getUserId())
                .taskType(originalMessage.getTaskType())
                .priority(originalMessage.getPriority())
                .timeoutMinutes(originalMessage.getTimeoutMinutes())
                .retryCount(originalMessage.getRetryCount() + 1)
                .maxRetryCount(originalMessage.getMaxRetryCount())
                .retryDelaySeconds(retryDelaySeconds)
                .scheduledTime(LocalDateTime.now().plusSeconds(retryDelaySeconds))
                .source("RETRY")
                .version(originalMessage.getVersion())
                .messageId(generateMessageId())
                .parentMessageId(originalMessage.getMessageId())
                .tags(originalMessage.getTags())
                .attributes(originalMessage.getAttributes() != null ? 
                           new HashMap<>(originalMessage.getAttributes()) : new HashMap<>())
                .build();
    }

    /**
     * 创建兜底任务消息
     * 
     * @param taskId   任务ID
     * @param userId   用户ID
     * @param taskType 任务类型
     * @return 兜底消息
     */
    public static TaskMessage fallback(String taskId, String userId, TaskType taskType) {
        return TaskMessage.builder()
                .taskId(taskId)
                .userId(userId)
                .taskType(taskType)
                .source("FALLBACK")
                .messageId(generateMessageId())
                .priority(1) // 兜底消息优先级最高
                .timeoutMinutes(taskType.getDefaultTimeoutMinutes())
                .build();
    }

    /**
     * 创建延迟任务消息
     * 
     * @param taskId        任务ID
     * @param userId        用户ID
     * @param taskType      任务类型
     * @param delayMinutes  延迟分钟数
     * @return 延迟消息
     */
    public static TaskMessage delayed(String taskId, String userId, TaskType taskType, int delayMinutes) {
        return TaskMessage.builder()
                .taskId(taskId)
                .userId(userId)
                .taskType(taskType)
                .source("DELAYED")
                .messageId(generateMessageId())
                .priority(taskType.getPriority())
                .timeoutMinutes(taskType.getDefaultTimeoutMinutes())
                .scheduledTime(LocalDateTime.now().plusMinutes(delayMinutes))
                .build();
    }

    // ==================== 便捷方法 ====================

    /**
     * 添加扩展属性
     * 
     * @param key   键
     * @param value 值
     * @return 当前对象，支持链式调用
     */
    public TaskMessage addAttribute(String key, Object value) {
        if (this.attributes == null) {
            this.attributes = new HashMap<>();
        }
        this.attributes.put(key, value);
        return this;
    }

    /**
     * 获取扩展属性
     * 
     * @param key 键
     * @return 值
     */
    public Object getAttribute(String key) {
        return this.attributes != null ? this.attributes.get(key) : null;
    }

    /**
     * 获取扩展属性（指定类型）
     * 
     * @param key   键
     * @param clazz 值类型
     * @param <T>   值类型泛型
     * @return 值
     */
    @SuppressWarnings("unchecked")
    public <T> T getAttribute(String key, Class<T> clazz) {
        Object value = getAttribute(key);
        if (value != null && clazz.isInstance(value)) {
            return (T) value;
        }
        return null;
    }

    /**
     * 添加调度上下文
     * 
     * @param key   键
     * @param value 值
     * @return 当前对象，支持链式调用
     */
    public TaskMessage addSchedulingContext(String key, Object value) {
        if (this.schedulingContext == null) {
            this.schedulingContext = new HashMap<>();
        }
        this.schedulingContext.put(key, value);
        return this;
    }

    /**
     * 检查是否可以重试
     * 
     * @return 是否可以重试
     */
    public boolean canRetry() {
        return this.retryCount < this.maxRetryCount;
    }

    /**
     * 检查是否为重试消息
     * 
     * @return 是否为重试消息
     */
    public boolean isRetryMessage() {
        return "RETRY".equals(this.source) || this.retryCount > 0;
    }

    /**
     * 检查是否为延迟消息
     * 
     * @return 是否为延迟消息
     */
    public boolean isDelayedMessage() {
        return this.scheduledTime != null && this.scheduledTime.isAfter(LocalDateTime.now());
    }

    /**
     * 检查是否到达执行时间
     * 
     * @return 是否到达执行时间
     */
    public boolean isReadyToExecute() {
        return this.scheduledTime == null || !this.scheduledTime.isAfter(LocalDateTime.now());
    }

    /**
     * 获取消息年龄（秒）
     * 
     * @return 消息年龄
     */
    public long getMessageAgeSeconds() {
        return java.time.Duration.between(this.sendTime, LocalDateTime.now()).getSeconds();
    }

    /**
     * 获取Redis队列主题
     * 
     * @return Redis队列主题
     */
    public String getRedisTopic() {
        return this.taskType != null ? this.taskType.getRedisTopic() : null;
    }

    /**
     * 生成消息ID
     * 
     * @return 消息ID
     */
    private static String generateMessageId() {
        return "MSG_" + System.currentTimeMillis() + "_" + 
               String.valueOf(Math.random()).substring(2, 8);
    }

    /**
     * 创建消息副本
     * 
     * @return 消息副本
     */
    public TaskMessage copy() {
        return TaskMessage.builder()
                .taskId(this.taskId)
                .userId(this.userId)
                .taskType(this.taskType)
                .priority(this.priority)
                .sendTime(this.sendTime)
                .scheduledTime(this.scheduledTime)
                .timeoutMinutes(this.timeoutMinutes)
                .retryCount(this.retryCount)
                .maxRetryCount(this.maxRetryCount)
                .retryDelaySeconds(this.retryDelaySeconds)
                .source(this.source)
                .version(this.version)
                .messageId(this.messageId)
                .parentMessageId(this.parentMessageId)
                .tags(this.tags)
                .attributes(this.attributes != null ? new HashMap<>(this.attributes) : new HashMap<>())
                .schedulingContext(this.schedulingContext != null ? new HashMap<>(this.schedulingContext) : new HashMap<>())
                .build();
    }

    @Override
    public String toString() {
        return String.format("TaskMessage{taskId='%s', userId='%s', taskType=%s, priority=%d, retryCount=%d}", 
                           taskId, userId, taskType, priority, retryCount);
    }
}
