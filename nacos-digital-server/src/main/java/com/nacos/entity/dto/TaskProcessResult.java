package com.nacos.entity.dto;

import com.nacos.entity.enums.UnifiedTaskStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 任务处理结果对象
 * 
 * 用于封装任务处理器的执行结果，包括：
 * 1. 处理状态和结果信息
 * 2. 外部API任务ID（用于后续状态查询）
 * 3. 结果数据和错误信息
 * 4. 执行统计信息
 * 
 * 设计原则：
 * 1. 统一的结果格式，便于调度器处理
 * 2. 包含足够的信息用于状态更新和错误处理
 * 3. 支持异步任务的中间状态
 * 4. 便于日志记录和监控统计
 * 
 * <AUTHOR>
 * @since 2025-07-31
 * @version 1.0 - 统一任务处理结果架构
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(description = "任务处理结果对象")
public class TaskProcessResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 处理是否成功
     */
    @Schema(description = "处理是否成功", required = true)
    private boolean success;

    /**
     * 结果状态
     * 如果不指定，将根据success字段自动推断
     */
    @Schema(description = "结果状态")
    private UnifiedTaskStatusEnum status;

    /**
     * 结果消息
     */
    @Schema(description = "结果消息")
    private String message;

    /**
     * 外部API任务ID
     * 用于异步任务的状态查询和回调处理
     */
    @Schema(description = "外部API任务ID")
    private String externalJobId;

    /**
     * 外部API响应数据
     */
    @Schema(description = "外部API响应数据")
    private String externalResponse;

    /**
     * 结果数据
     * 存储任务执行产生的具体结果
     */
    @Schema(description = "结果数据")
    @Builder.Default
    private Map<String, Object> resultData = new HashMap<>();

    /**
     * 错误代码
     */
    @Schema(description = "错误代码")
    private String errorCode;

    /**
     * 错误详情
     */
    @Schema(description = "错误详情")
    private String errorDetail;

    /**
     * 异常堆栈信息
     */
    @Schema(description = "异常堆栈信息")
    private String stackTrace;

    /**
     * 处理开始时间
     */
    @Schema(description = "处理开始时间")
    private LocalDateTime startTime;

    /**
     * 处理结束时间
     */
    @Schema(description = "处理结束时间")
    private LocalDateTime endTime;

    /**
     * 处理耗时（毫秒）
     */
    @Schema(description = "处理耗时（毫秒）")
    private Long processingTimeMs;

    /**
     * 是否需要异步回调
     */
    @Schema(description = "是否需要异步回调")
    @Builder.Default
    private boolean needsCallback = false;

    /**
     * 预计回调时间（分钟）
     */
    @Schema(description = "预计回调时间（分钟）")
    private Integer estimatedCallbackMinutes;

    /**
     * 重试建议
     */
    @Schema(description = "重试建议")
    @Builder.Default
    private boolean retryable = false;

    /**
     * 建议重试延迟（秒）
     */
    @Schema(description = "建议重试延迟（秒）")
    private Integer retryDelaySeconds;

    /**
     * 扩展属性
     */
    @Schema(description = "扩展属性")
    @Builder.Default
    private Map<String, Object> attributes = new HashMap<>();

    // ==================== 静态工厂方法 ====================

    /**
     * 创建成功结果
     * 
     * @return 成功结果
     */
    public static TaskProcessResult success() {
        return TaskProcessResult.builder()
                .success(true)
                .status(UnifiedTaskStatusEnum.SUCCESS)
                .message("处理成功")
                .endTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建成功结果（带消息）
     * 
     * @param message 成功消息
     * @return 成功结果
     */
    public static TaskProcessResult success(String message) {
        return TaskProcessResult.builder()
                .success(true)
                .status(UnifiedTaskStatusEnum.SUCCESS)
                .message(message)
                .endTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建成功结果（带外部任务ID）
     * 
     * @param externalJobId 外部任务ID
     * @return 成功结果
     */
    public static TaskProcessResult success(String message, String externalJobId) {
        return TaskProcessResult.builder()
                .success(true)
                .status(UnifiedTaskStatusEnum.SUCCESS)
                .message(message)
                .externalJobId(externalJobId)
                .endTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建进行中结果（异步任务）
     * 
     * @param externalJobId 外部任务ID
     * @return 进行中结果
     */
    public static TaskProcessResult processing(String externalJobId) {
        return TaskProcessResult.builder()
                .success(true)
                .status(UnifiedTaskStatusEnum.PROGRESS)
                .message("任务已提交，正在处理中")
                .externalJobId(externalJobId)
                .needsCallback(true)
                .endTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建进行中结果（带预计回调时间）
     * 
     * @param externalJobId              外部任务ID
     * @param estimatedCallbackMinutes   预计回调时间
     * @return 进行中结果
     */
    public static TaskProcessResult processing(String externalJobId, int estimatedCallbackMinutes) {
        return TaskProcessResult.builder()
                .success(true)
                .status(UnifiedTaskStatusEnum.PROGRESS)
                .message("任务已提交，正在处理中")
                .externalJobId(externalJobId)
                .needsCallback(true)
                .estimatedCallbackMinutes(estimatedCallbackMinutes)
                .endTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建失败结果
     * 
     * @param message 失败消息
     * @return 失败结果
     */
    public static TaskProcessResult failure(String message) {
        return TaskProcessResult.builder()
                .success(false)
                .status(UnifiedTaskStatusEnum.FAILED)
                .message(message)
                .endTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建失败结果（带错误代码）
     * 
     * @param message   失败消息
     * @param errorCode 错误代码
     * @return 失败结果
     */
    public static TaskProcessResult failure(String message, String errorCode) {
        return TaskProcessResult.builder()
                .success(false)
                .status(UnifiedTaskStatusEnum.FAILED)
                .message(message)
                .errorCode(errorCode)
                .endTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建可重试的失败结果
     * 
     * @param message           失败消息
     * @param retryDelaySeconds 重试延迟秒数
     * @return 失败结果
     */
    public static TaskProcessResult retryableFailure(String message, int retryDelaySeconds) {
        return TaskProcessResult.builder()
                .success(false)
                .status(UnifiedTaskStatusEnum.FAILED)
                .message(message)
                .retryable(true)
                .retryDelaySeconds(retryDelaySeconds)
                .endTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建超时结果
     * 
     * @param message 超时消息
     * @return 超时结果
     */
    public static TaskProcessResult timeout(String message) {
        return TaskProcessResult.builder()
                .success(false)
                .status(UnifiedTaskStatusEnum.TIMEOUT)
                .message(message)
                .endTime(LocalDateTime.now())
                .build();
    }

    // ==================== 便捷方法 ====================

    /**
     * 添加结果数据
     * 
     * @param key   键
     * @param value 值
     * @return 当前对象，支持链式调用
     */
    public TaskProcessResult addResultData(String key, Object value) {
        if (this.resultData == null) {
            this.resultData = new HashMap<>();
        }
        this.resultData.put(key, value);
        return this;
    }

    /**
     * 添加扩展属性
     * 
     * @param key   键
     * @param value 值
     * @return 当前对象，支持链式调用
     */
    public TaskProcessResult addAttribute(String key, Object value) {
        if (this.attributes == null) {
            this.attributes = new HashMap<>();
        }
        this.attributes.put(key, value);
        return this;
    }

    /**
     * 设置处理时间
     * 
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 当前对象，支持链式调用
     */
    public TaskProcessResult setProcessingTime(LocalDateTime startTime, LocalDateTime endTime) {
        this.startTime = startTime;
        this.endTime = endTime;
        if (startTime != null && endTime != null) {
            this.processingTimeMs = java.time.Duration.between(startTime, endTime).toMillis();
        }
        return this;
    }

    /**
     * 自动推断状态
     * 如果未设置状态，根据success字段自动推断
     */
    public void inferStatus() {
        if (this.status == null) {
            this.status = this.success ? UnifiedTaskStatusEnum.SUCCESS : UnifiedTaskStatusEnum.FAILED;
        }
    }

    /**
     * 检查是否为最终状态
     * 
     * @return 是否为最终状态
     */
    public boolean isFinalStatus() {
        return this.status != null && this.status.isFinalStatus();
    }

    @Override
    public String toString() {
        return String.format("TaskProcessResult{success=%s, status=%s, message='%s', externalJobId='%s'}", 
                           success, status, message, externalJobId);
    }
}
