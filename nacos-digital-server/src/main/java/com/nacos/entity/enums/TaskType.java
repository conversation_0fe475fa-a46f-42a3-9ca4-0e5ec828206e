package com.nacos.entity.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

/**
 * 任务类型枚举
 * 
 * 定义系统支持的所有任务类型，每种类型对应不同的处理器实现
 * 
 * 设计原则：
 * 1. 使用英文常量名，便于代码中使用
 * 2. 包含中文描述，便于日志和界面显示
 * 3. 支持Redis队列主题映射
 * 4. 预留扩展空间，便于新增任务类型
 * 
 * <AUTHOR>
 * @since 2025-07-31
 * @version 1.0 - 统一任务类型架构
 */
@Getter
@Schema(description = "任务类型枚举")
public enum TaskType {
    
    /**
     * 视频翻译任务
     * 对应实体：VideoTranslateTaskPO
     * 参数字段：requestParamsJson
     * 外部API：羚羊平台等
     */
    VIDEO_TRANSLATE("VIDEO_TRANSLATE", "视频翻译", "TOPIC_VIDEO_TRANSLATE", 
                   "com.nacos.entity.po.VideoTranslateTaskPO", "requestParamsJson"),
    
    /**
     * 音频生成任务
     * 对应实体：DigitalAudioTaskPO
     * 参数字段：requestParamsJson
     * 外部API：MINIMAX等服务商
     */
    AUDIO_GENERATE("AUDIO_GENERATE", "音频生成", "TOPIC_AUDIO_GENERATE", 
                  "com.nacos.entity.po.DigitalAudioTaskPO", "requestParamsJson"),
    
    /**
     * 视频编辑任务
     * 对应实体：VideoEditTaskPO
     * 参数字段：taskParameters
     * 外部API：多种供应商
     */
    VIDEO_EDIT("VIDEO_EDIT", "视频编辑", "TOPIC_VIDEO_EDIT", 
              "com.nacos.entity.po.VideoEditTaskPO", "taskParameters");

    /**
     * 任务类型代码
     */
    @Schema(description = "任务类型代码")
    private final String code;
    
    /**
     * 任务类型描述
     */
    @Schema(description = "任务类型描述")
    private final String description;
    
    /**
     * Redis消息队列主题
     */
    @Schema(description = "Redis消息队列主题")
    private final String redisTopic;
    
    /**
     * 对应的PO实体类全名
     */
    @Schema(description = "对应的PO实体类全名")
    private final String entityClassName;
    
    /**
     * 参数存储字段名
     */
    @Schema(description = "参数存储字段名")
    private final String parameterField;

    TaskType(String code, String description, String redisTopic, 
             String entityClassName, String parameterField) {
        this.code = code;
        this.description = description;
        this.redisTopic = redisTopic;
        this.entityClassName = entityClassName;
        this.parameterField = parameterField;
    }

    /**
     * 根据任务类型代码获取枚举
     * 
     * @param code 任务类型代码
     * @return 对应的枚举，如果不存在返回null
     */
    public static TaskType fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }
        
        for (TaskType taskType : values()) {
            if (taskType.code.equals(code.trim())) {
                return taskType;
            }
        }
        return null;
    }

    /**
     * 根据Redis主题获取任务类型
     * 
     * @param redisTopic Redis主题名称
     * @return 对应的枚举，如果不存在返回null
     */
    public static TaskType fromRedisTopic(String redisTopic) {
        if (redisTopic == null || redisTopic.trim().isEmpty()) {
            return null;
        }
        
        for (TaskType taskType : values()) {
            if (taskType.redisTopic.equals(redisTopic.trim())) {
                return taskType;
            }
        }
        return null;
    }

    /**
     * 根据实体类名获取任务类型
     * 
     * @param entityClassName 实体类全名
     * @return 对应的枚举，如果不存在返回null
     */
    public static TaskType fromEntityClassName(String entityClassName) {
        if (entityClassName == null || entityClassName.trim().isEmpty()) {
            return null;
        }
        
        for (TaskType taskType : values()) {
            if (taskType.entityClassName.equals(entityClassName.trim())) {
                return taskType;
            }
        }
        return null;
    }

    /**
     * 获取处理器Bean名称
     * 遵循Spring Bean命名规范：首字母小写的驼峰命名
     * 
     * @return 处理器Bean名称
     */
    public String getProcessorBeanName() {
        switch (this) {
            case VIDEO_TRANSLATE:
                return "videoTranslateProcessor";
            case AUDIO_GENERATE:
                return "audioGenerateProcessor";
            case VIDEO_EDIT:
                return "videoEditProcessor";
            default:
                // 通用规则：去掉下划线，转为驼峰命名，末尾加Processor
                String[] parts = this.code.toLowerCase().split("_");
                StringBuilder beanName = new StringBuilder(parts[0]);
                for (int i = 1; i < parts.length; i++) {
                    beanName.append(Character.toUpperCase(parts[i].charAt(0)))
                           .append(parts[i].substring(1));
                }
                beanName.append("Processor");
                return beanName.toString();
        }
    }

    /**
     * 获取服务Bean名称
     * 
     * @return 服务Bean名称
     */
    public String getServiceBeanName() {
        switch (this) {
            case VIDEO_TRANSLATE:
                return "videoTranslateAsyncService";
            case AUDIO_GENERATE:
                return "digitalAudioTaskService";
            case VIDEO_EDIT:
                return "videoEditService";
            default:
                // 通用规则
                String[] parts = this.code.toLowerCase().split("_");
                StringBuilder beanName = new StringBuilder(parts[0]);
                for (int i = 1; i < parts.length; i++) {
                    beanName.append(Character.toUpperCase(parts[i].charAt(0)))
                           .append(parts[i].substring(1));
                }
                beanName.append("Service");
                return beanName.toString();
        }
    }

    /**
     * 获取数据库表名
     * 
     * @return 数据库表名
     */
    public String getTableName() {
        switch (this) {
            case VIDEO_TRANSLATE:
                return "digital_video_translation_task";
            case AUDIO_GENERATE:
                return "digital_audio_task";
            case VIDEO_EDIT:
                return "digital_video_edit_task";
            default:
                // 通用规则：转为下划线分隔的小写形式
                return "digital_" + this.code.toLowerCase().replace("_", "_") + "_task";
        }
    }

    /**
     * 检查是否支持异步回调
     * 
     * @return 是否支持异步回调
     */
    public boolean supportsAsyncCallback() {
        // 所有任务类型都支持异步回调
        return true;
    }

    /**
     * 获取默认超时时间（分钟）
     * 
     * @return 默认超时时间
     */
    public int getDefaultTimeoutMinutes() {
        switch (this) {
            case VIDEO_TRANSLATE:
                return 30; // 视频翻译默认30分钟超时
            case AUDIO_GENERATE:
                return 15; // 音频生成默认15分钟超时
            case VIDEO_EDIT:
                return 60; // 视频编辑默认60分钟超时
            default:
                return 30; // 默认30分钟
        }
    }

    /**
     * 获取任务优先级
     * 数值越小优先级越高
     * 
     * @return 任务优先级
     */
    public int getPriority() {
        switch (this) {
            case VIDEO_TRANSLATE:
                return 1; // 视频翻译优先级最高
            case AUDIO_GENERATE:
                return 2; // 音频生成优先级中等
            case VIDEO_EDIT:
                return 3; // 视频编辑优先级较低
            default:
                return 5; // 默认优先级
        }
    }

    @Override
    public String toString() {
        return String.format("TaskType{code='%s', description='%s', redisTopic='%s'}", 
                           code, description, redisTopic);
    }
}
