package com.nacos.entity.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

/**
 * 统一任务状态枚举
 * 
 * 设计原则：
 * 1. 失败(0)和成功(1)固定不变，便于前端判断
 * 2. 新增状态从6开始，不影响现有逻辑
 * 3. 符合业务流程的自然顺序：排队→进行→成功/失败/超时/取消
 * 
 * 状态流转：
 * QUEUING(2) → PROGRESS(3) → SUCCESS(1) | FAILED(0) | TIMEOUT(4) | CANCELLED(5)
 * 
 * <AUTHOR>
 * @since 2025-07-31
 * @version 1.0 - 统一任务状态架构
 */
@Getter
@Schema(description = "统一任务状态枚举")
public enum UnifiedTaskStatusEnum {
    
    /**
     * 失败状态 - 固定为0，便于前端判断
     */
    FAILED(0, "失败", "任务执行失败", false, true),
    
    /**
     * 成功状态 - 固定为1，便于前端判断
     */
    SUCCESS(1, "成功", "任务执行成功", false, true),
    
    /**
     * 排队中状态 - 任务已提交，等待处理
     */
    QUEUING(2, "排队中", "任务已提交，等待处理", true, false),
    
    /**
     * 进行中状态 - 任务正在执行
     */
    PROGRESS(3, "进行中", "任务正在执行", true, false),
    
    /**
     * 超时状态 - 任务执行超时
     */
    TIMEOUT(4, "超时", "任务执行超时", false, true),
    
    /**
     * 已取消状态 - 任务被用户或系统取消
     */
    CANCELLED(5, "已取消", "任务已被取消", false, true);

    /**
     * 状态码
     */
    @Schema(description = "状态码")
    private final Integer code;
    
    /**
     * 状态名称
     */
    @Schema(description = "状态名称")
    private final String name;
    
    /**
     * 状态描述
     */
    @Schema(description = "状态描述")
    private final String description;
    
    /**
     * 是否为处理中状态（可以继续处理）
     */
    @Schema(description = "是否为处理中状态")
    private final boolean processing;
    
    /**
     * 是否为最终状态（不可再变更）
     */
    @Schema(description = "是否为最终状态")
    private final boolean finalStatus;

    UnifiedTaskStatusEnum(Integer code, String name, String description, boolean processing, boolean finalStatus) {
        this.code = code;
        this.name = name;
        this.description = description;
        this.processing = processing;
        this.finalStatus = finalStatus;
    }

    /**
     * 根据状态码获取枚举
     * 
     * @param code 状态码
     * @return 对应的枚举，如果不存在返回null
     */
    public static UnifiedTaskStatusEnum fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        
        for (UnifiedTaskStatusEnum status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据状态名称获取枚举
     * 
     * @param name 状态名称
     * @return 对应的枚举，如果不存在返回null
     */
    public static UnifiedTaskStatusEnum fromName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return null;
        }
        
        for (UnifiedTaskStatusEnum status : values()) {
            if (status.name.equals(name.trim())) {
                return status;
            }
        }
        return null;
    }

    /**
     * 检查状态是否可以转换到目标状态
     * 
     * @param targetStatus 目标状态
     * @return 是否可以转换
     */
    public boolean canTransitionTo(UnifiedTaskStatusEnum targetStatus) {
        if (targetStatus == null) {
            return false;
        }
        
        // 最终状态不能再转换
        if (this.finalStatus) {
            return false;
        }
        
        // 定义允许的状态转换规则
        switch (this) {
            case QUEUING:
                // 排队中可以转换为：进行中、失败、取消
                return targetStatus == PROGRESS || targetStatus == FAILED || targetStatus == CANCELLED;
                
            case PROGRESS:
                // 进行中可以转换为：成功、失败、超时、取消
                return targetStatus == SUCCESS || targetStatus == FAILED || 
                       targetStatus == TIMEOUT || targetStatus == CANCELLED;
                
            default:
                return false;
        }
    }

    /**
     * 获取所有处理中的状态
     * 
     * @return 处理中状态数组
     */
    public static UnifiedTaskStatusEnum[] getProcessingStatuses() {
        return new UnifiedTaskStatusEnum[]{QUEUING, PROGRESS};
    }

    /**
     * 获取所有最终状态
     * 
     * @return 最终状态数组
     */
    public static UnifiedTaskStatusEnum[] getFinalStatuses() {
        return new UnifiedTaskStatusEnum[]{SUCCESS, FAILED, TIMEOUT, CANCELLED};
    }

    /**
     * 检查是否为成功状态
     * 
     * @return 是否成功
     */
    public boolean isSuccess() {
        return this == SUCCESS;
    }

    /**
     * 检查是否为失败相关状态（失败、超时）
     * 
     * @return 是否失败
     */
    public boolean isFailure() {
        return this == FAILED || this == TIMEOUT;
    }

    /**
     * 检查是否为取消状态
     * 
     * @return 是否取消
     */
    public boolean isCancelled() {
        return this == CANCELLED;
    }

    /**
     * 获取状态的显示样式（用于前端展示）
     * 
     * @return CSS样式类名
     */
    public String getDisplayStyle() {
        switch (this) {
            case SUCCESS:
                return "success";
            case FAILED:
            case TIMEOUT:
                return "error";
            case CANCELLED:
                return "warning";
            case QUEUING:
            case PROGRESS:
                return "processing";
            default:
                return "default";
        }
    }

    @Override
    public String toString() {
        return String.format("UnifiedTaskStatus{code=%d, name='%s', description='%s'}", 
                           code, name, description);
    }
}
