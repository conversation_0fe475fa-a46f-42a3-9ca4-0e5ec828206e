package com.nacos.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nacos.entity.po.VideoTranslateTaskPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 视频翻译任务Mapper接口
 * 基于羚羊平台的视频翻译任务数据访问层
 * 参考DigitalVideoTaskMapper和VideoEditTaskMapper的设计模式
 * 
 * <AUTHOR>
 * @since 2025-01-29
 */
@Mapper
public interface VideoTranslateTaskMapper extends BaseMapper<VideoTranslateTaskPO> {

    /**
     * 根据用户ID查询视频翻译任务列表
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 任务列表
     */
    @Select("SELECT * FROM digital_video_translation_task WHERE user_id = #{userId} AND is_deleted = 0 ORDER BY created_time DESC LIMIT #{limit}")
    List<VideoTranslateTaskPO> getTasksByUserId(@Param("userId") String userId, @Param("limit") Integer limit);

    /**
     * 根据状态查询任务列表
     * @param status 任务状态
     * @param limit 限制数量
     * @return 任务列表
     */
    @Select("SELECT * FROM digital_video_translation_task WHERE status = #{status} AND is_deleted = 0 ORDER BY created_time ASC LIMIT #{limit}")
    List<VideoTranslateTaskPO> getTasksByStatus(@Param("status") String status, @Param("limit") Integer limit);

    /**
     * 查询处理中的任务（需要状态同步）
     * 包括QUEUING(2)、PROGRESS(3)状态的任务
     * @param limit 限制数量
     * @return 任务列表
     */
    @Select("SELECT * FROM digital_video_translation_task WHERE status IN (2, 3) AND is_deleted = 0 ORDER BY created_time ASC LIMIT #{limit}")
    List<VideoTranslateTaskPO> getProcessingTasks(@Param("limit") Integer limit);

    /**
     * 查询超时任务
     * 查询创建时间超过指定分钟数且仍在处理中的任务
     * @param timeoutMinutes 超时分钟数
     * @return 任务列表
     */
    @Select("SELECT * FROM digital_video_translation_task WHERE status IN (2, 3) " +
            "AND is_deleted = 0 AND created_time < DATE_SUB(NOW(), INTERVAL #{timeoutMinutes} MINUTE) " +
            "ORDER BY created_time ASC")
    List<VideoTranslateTaskPO> getTimeoutTasks(@Param("timeoutMinutes") Integer timeoutMinutes);

    /**
     * 统计用户的任务数量
     * @param userId 用户ID
     * @param status 任务状态，null表示统计所有状态
     * @return 任务数量
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM digital_video_translation_task WHERE user_id = #{userId} AND is_deleted = 0" +
            "<if test='status != null'> AND status = #{status}</if>" +
            "</script>")
    Integer countTasksByUser(@Param("userId") String userId, @Param("status") Integer status);

    /**
     * 根据任务ID查询任务详情
     * @param taskId 任务ID
     * @return 任务详情
     */
    @Select("SELECT * FROM digital_video_translation_task WHERE task_id = #{taskId} AND is_deleted = 0")
    VideoTranslateTaskPO getTaskByTaskId(@Param("taskId") String taskId);

    /**
     * 根据羚羊平台任务ID查询任务
     * @param lingyangTaskId 羚羊平台任务ID
     * @return 任务详情
     */
    @Select("SELECT * FROM digital_video_translation_task WHERE lingyang_task_id = #{lingyangTaskId} AND is_deleted = 0")
    VideoTranslateTaskPO getTaskByLingyangTaskId(@Param("lingyangTaskId") String lingyangTaskId);

    /**
     * 查询用户在指定时间范围内的任务数量
     * @param userId 用户ID
     * @param startTime 开始时间（格式：yyyy-MM-dd HH:mm:ss）
     * @param endTime 结束时间（格式：yyyy-MM-dd HH:mm:ss）
     * @return 任务数量
     */
    @Select("SELECT COUNT(*) FROM digital_video_translation_task WHERE user_id = #{userId} AND is_deleted = 0 " +
            "AND created_time >= #{startTime} AND created_time <= #{endTime}")
    Integer countTasksByUserAndTimeRange(@Param("userId") String userId, 
                                       @Param("startTime") String startTime, 
                                       @Param("endTime") String endTime);

    /**
     * 查询指定状态的任务数量
     * @param status 任务状态
     * @return 任务数量
     */
    @Select("SELECT COUNT(*) FROM digital_video_translation_task WHERE status = #{status} AND is_deleted = 0")
    Integer countTasksByStatus(@Param("status") Integer status);

    /**
     * 查询用户最近的任务
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 任务列表
     */
    @Select("SELECT * FROM digital_video_translation_task WHERE user_id = #{userId} AND is_deleted = 0 " +
            "ORDER BY created_time DESC LIMIT #{limit}")
    List<VideoTranslateTaskPO> getRecentTasksByUserId(@Param("userId") String userId, @Param("limit") Integer limit);

    /**
     * 查询成功完成的任务
     * @param userId 用户ID（可选）
     * @param limit 限制数量
     * @return 任务列表
     */
    @Select("<script>" +
            "SELECT * FROM digital_video_translation_task WHERE status = 1 AND is_deleted = 0" +
            "<if test='userId != null'> AND user_id = #{userId}</if>" +
            " ORDER BY update_time DESC LIMIT #{limit}" +
            "</script>")
    List<VideoTranslateTaskPO> getCompletedTasks(@Param("userId") String userId, @Param("limit") Integer limit);

    /**
     * 查询失败的任务
     * @param userId 用户ID（可选）
     * @param limit 限制数量
     * @return 任务列表
     */
    @Select("<script>" +
            "SELECT * FROM digital_video_translation_task WHERE status IN (0, 4) AND is_deleted = 0" +
            "<if test='userId != null'> AND user_id = #{userId}</if>" +
            " ORDER BY update_time DESC LIMIT #{limit}" +
            "</script>")
    List<VideoTranslateTaskPO> getFailedTasks(@Param("userId") String userId, @Param("limit") Integer limit);
}
