package com.nacos.service;

import com.nacos.entity.dto.CallbackData;
import com.nacos.entity.dto.TaskMessage;
import com.nacos.entity.dto.TaskProcessResult;
import com.nacos.entity.enums.TaskType;
import com.nacos.entity.enums.UnifiedTaskStatusEnum;
import com.nacos.result.Result;

import java.util.List;
import java.util.Map;

/**
 * 统一任务服务接口
 * 
 * 提供统一的任务管理服务，包括任务提交、查询、取消、状态管理等功能。
 * 基于策略模式+工厂模式的架构，支持多种任务类型的统一处理。
 * 
 * 设计原则：
 * 1. 统一接口：为所有任务类型提供一致的服务接口
 * 2. 类型安全：通过TaskType枚举确保任务类型的正确性
 * 3. 状态统一：使用UnifiedTaskStatusEnum统一状态管理
 * 4. 异常安全：所有方法都有完善的异常处理
 * 5. 可扩展性：支持新任务类型的快速接入
 * 
 * 支持的任务类型：
 * - VIDEO_TRANSLATE：视频翻译任务
 * - AUDIO_GENERATE：音频生成任务
 * - VIDEO_EDIT：视频编辑任务
 * 
 * <AUTHOR>
 * @since 2025-07-31
 * @version 1.0 - 统一任务服务架构
 */
public interface UniversalTaskService {

    // ==================== 核心任务管理方法 ====================

    /**
     * 提交任务到队列
     * 
     * 将任务消息提交到对应的Redis队列中，由消息监听器异步处理。
     * 这是任务处理的统一入口点。
     * 
     * @param message 任务消息，包含任务ID、类型、用户ID等信息
     * @return 提交结果，包含任务ID和提交状态
     * @throws IllegalArgumentException 当消息参数无效时
     */
    Result<Map<String, Object>> submitTask(TaskMessage message);

    /**
     * 同步处理任务
     * 
     * 直接调用调度器处理任务，不经过消息队列。
     * 适用于需要立即获取处理结果的场景。
     * 
     * @param taskId 任务ID，不能为null或空
     * @return 处理结果，包含状态、消息、外部任务ID等信息
     * @throws IllegalArgumentException 当任务ID无效时
     */
    Result<TaskProcessResult> processTaskSync(String taskId);

    /**
     * 处理异步回调
     * 
     * 处理来自外部API的异步回调通知，更新任务状态和结果。
     * 支持重复回调的幂等性处理。
     * 
     * @param taskId       任务ID，不能为null或空
     * @param callbackData 回调数据，包含状态、结果等信息
     * @return 处理结果
     * @throws IllegalArgumentException 当参数无效时
     */
    Result<Boolean> handleCallback(String taskId, CallbackData callbackData);

    /**
     * 取消任务
     * 
     * 取消正在执行或排队中的任务。对于已经提交到外部API的任务，
     * 会尝试调用外部API的取消接口。
     * 
     * @param taskId 任务ID，不能为null或空
     * @param userId 用户ID，用于权限验证
     * @param reason 取消原因，可选
     * @return 取消结果
     * @throws IllegalArgumentException 当参数无效时
     */
    Result<Boolean> cancelTask(String taskId, String userId, String reason);

    // ==================== 任务查询方法 ====================

    /**
     * 查询任务状态
     * 
     * 获取任务的详细状态信息，包括进度、错误信息、结果等。
     * 
     * @param taskId 任务ID，不能为null或空
     * @return 任务状态信息
     * @throws IllegalArgumentException 当任务ID无效时
     */
    Result<Map<String, Object>> getTaskStatus(String taskId);

    /**
     * 查询任务状态（指定类型）
     * 
     * 当已知任务类型时，可以获得更详细的类型特定信息。
     * 
     * @param taskId   任务ID，不能为null或空
     * @param taskType 任务类型，不能为null
     * @return 任务状态信息
     * @throws IllegalArgumentException 当参数无效时
     */
    Result<Map<String, Object>> getTaskStatus(String taskId, TaskType taskType);

    /**
     * 批量查询任务状态
     * 
     * 一次查询多个任务的状态，提高查询效率。
     * 
     * @param taskIds 任务ID列表，不能为null或空
     * @return 任务状态映射，key为任务ID，value为状态信息
     * @throws IllegalArgumentException 当参数无效时
     */
    Result<Map<String, Map<String, Object>>> batchGetTaskStatus(List<String> taskIds);

    /**
     * 查询用户任务列表
     * 
     * 获取指定用户的任务列表，支持状态过滤和分页。
     * 
     * @param userId     用户ID，不能为null或空
     * @param taskType   任务类型，null表示查询所有类型
     * @param status     任务状态，null表示查询所有状态
     * @param pageNum    页码，从1开始
     * @param pageSize   每页大小，最大100
     * @return 任务列表和分页信息
     * @throws IllegalArgumentException 当参数无效时
     */
    Result<Map<String, Object>> getUserTasks(String userId, TaskType taskType, 
                                           UnifiedTaskStatusEnum status, 
                                           Integer pageNum, Integer pageSize);

    // ==================== 任务统计方法 ====================

    /**
     * 获取用户任务统计
     * 
     * 统计用户各种状态的任务数量、总处理时间等信息。
     * 
     * @param userId 用户ID，不能为null或空
     * @return 统计信息
     * @throws IllegalArgumentException 当用户ID无效时
     */
    Result<Map<String, Object>> getUserTaskStatistics(String userId);

    /**
     * 获取系统任务统计
     * 
     * 获取系统级别的任务统计信息，包括各类型任务的处理情况。
     * 
     * @param taskType 任务类型，null表示统计所有类型
     * @return 统计信息
     */
    Result<Map<String, Object>> getSystemTaskStatistics(TaskType taskType);

    // ==================== 任务管理方法 ====================

    /**
     * 重试失败的任务
     * 
     * 重新提交失败的任务进行处理。会重置任务状态并重新提交到队列。
     * 
     * @param taskId 任务ID，不能为null或空
     * @param userId 用户ID，用于权限验证
     * @return 重试结果
     * @throws IllegalArgumentException 当参数无效时
     */
    Result<Boolean> retryTask(String taskId, String userId);

    /**
     * 批量重试失败的任务
     * 
     * 批量重试多个失败的任务，提高操作效率。
     * 
     * @param taskIds 任务ID列表，不能为null或空
     * @param userId  用户ID，用于权限验证
     * @return 重试结果统计
     * @throws IllegalArgumentException 当参数无效时
     */
    Result<Map<String, Object>> batchRetryTasks(List<String> taskIds, String userId);

    /**
     * 清理过期任务
     * 
     * 清理指定时间之前的已完成或已取消任务，释放存储空间。
     * 
     * @param beforeDays 保留天数，超过此天数的任务将被清理
     * @param taskType   任务类型，null表示清理所有类型
     * @return 清理结果统计
     * @throws IllegalArgumentException 当参数无效时
     */
    Result<Map<String, Object>> cleanupExpiredTasks(Integer beforeDays, TaskType taskType);

    // ==================== 系统管理方法 ====================

    /**
     * 获取服务健康状态
     * 
     * 检查任务服务的健康状态，包括调度器、处理器工厂、数据库连接等。
     * 
     * @return 健康状态信息
     */
    Result<Map<String, Object>> getHealthStatus();

    /**
     * 获取服务统计信息
     * 
     * 获取任务服务的运行统计信息，包括处理量、成功率、平均处理时间等。
     * 
     * @return 统计信息
     */
    Result<Map<String, Object>> getServiceStatistics();

    /**
     * 刷新处理器缓存
     *
     * 刷新任务处理器工厂的缓存，重新加载处理器配置。
     *
     * @return 刷新结果
     */
    Result<Boolean> refreshProcessorCache();

    /**
     * 检查消息队列健康状态
     *
     * 检查所有任务类型对应的Redis消息队列的健康状态，包括连接状态、队列可达性等。
     *
     * @return 队列健康状态报告
     */
    Result<Map<String, Object>> checkQueueHealth();
}
