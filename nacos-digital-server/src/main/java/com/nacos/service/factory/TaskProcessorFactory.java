package com.nacos.service.factory;

import com.nacos.entity.enums.TaskType;
import com.nacos.service.processor.UniversalTaskProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 任务处理器工厂
 * 
 * 负责管理和提供所有任务处理器实例，包括：
 * 1. 自动注册Spring容器中的所有任务处理器
 * 2. 根据任务类型快速获取对应的处理器
 * 3. 处理器健康检查和状态管理
 * 4. 处理器统计信息和监控支持
 * 
 * 设计原则：
 * 1. 单例模式：全局唯一的工厂实例
 * 2. 线程安全：使用ConcurrentHashMap确保并发安全
 * 3. 自动发现：利用Spring依赖注入自动注册处理器
 * 4. 故障隔离：单个处理器故障不影响其他处理器
 * 
 * <AUTHOR>
 * @since 2025-07-31
 * @version 1.0 - 统一任务处理器工厂架构
 */
@Component
@Slf4j
public class TaskProcessorFactory implements ApplicationContextAware, InitializingBean {

    /**
     * Spring应用上下文
     */
    private ApplicationContext applicationContext;

    /**
     * 任务类型到处理器的映射
     * 使用ConcurrentHashMap确保线程安全
     */
    private final Map<TaskType, UniversalTaskProcessor<?>> processorMap = new ConcurrentHashMap<>();

    /**
     * 处理器注册统计
     */
    private final Map<String, Object> registrationStats = new ConcurrentHashMap<>();

    /**
     * 工厂初始化时间
     */
    private long initializationTime;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
        log.debug("TaskProcessorFactory received ApplicationContext");
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        log.info("Initializing TaskProcessorFactory...");
        this.initializationTime = System.currentTimeMillis();
        
        try {
            registerAllProcessors();
            validateProcessorRegistration();
            logRegistrationSummary();
            
            log.info("TaskProcessorFactory initialized successfully with {} processors", 
                    processorMap.size());
        } catch (Exception e) {
            log.error("Failed to initialize TaskProcessorFactory", e);
            throw new IllegalStateException("TaskProcessorFactory initialization failed", e);
        }
    }

    /**
     * 自动注册所有任务处理器
     */
    private void registerAllProcessors() {
        log.debug("Starting automatic processor registration...");
        
        // 获取所有UniversalTaskProcessor类型的Bean
        Map<String, UniversalTaskProcessor> processorBeans = 
                applicationContext.getBeansOfType(UniversalTaskProcessor.class);
        
        int successCount = 0;
        int failureCount = 0;
        
        for (Map.Entry<String, UniversalTaskProcessor> entry : processorBeans.entrySet()) {
            String beanName = entry.getKey();
            UniversalTaskProcessor<?> processor = entry.getValue();
            
            try {
                registerProcessor(processor, beanName);
                successCount++;
            } catch (Exception e) {
                log.error("Failed to register processor bean: {}", beanName, e);
                failureCount++;
            }
        }
        
        // 更新注册统计
        registrationStats.put("totalFound", processorBeans.size());
        registrationStats.put("successCount", successCount);
        registrationStats.put("failureCount", failureCount);
        registrationStats.put("registrationTime", System.currentTimeMillis());
        
        log.info("Processor registration completed: {} success, {} failure, {} total", 
                successCount, failureCount, processorBeans.size());
    }

    /**
     * 注册单个处理器
     * 
     * @param processor 处理器实例
     * @param beanName  Bean名称
     */
    private void registerProcessor(UniversalTaskProcessor<?> processor, String beanName) {
        if (processor == null) {
            throw new IllegalArgumentException("Processor cannot be null");
        }
        
        TaskType taskType = processor.getSupportedTaskType();
        if (taskType == null) {
            throw new IllegalArgumentException(
                    String.format("Processor %s returned null task type", beanName));
        }
        
        // 检查重复注册
        if (processorMap.containsKey(taskType)) {
            UniversalTaskProcessor<?> existingProcessor = processorMap.get(taskType);
            log.warn("Duplicate processor registration for task type {}: existing={}, new={}", 
                    taskType, existingProcessor.getClass().getSimpleName(), 
                    processor.getClass().getSimpleName());
            throw new IllegalStateException(
                    String.format("Task type %s already has a registered processor", taskType));
        }
        
        // 初始化处理器
        try {
            processor.initialize();
            log.debug("Processor {} initialized successfully", beanName);
        } catch (Exception e) {
            log.error("Failed to initialize processor {}", beanName, e);
            throw new RuntimeException("Processor initialization failed", e);
        }
        
        // 注册处理器
        processorMap.put(taskType, processor);
        log.info("Registered processor: {} -> {} (bean: {})", 
                taskType, processor.getClass().getSimpleName(), beanName);
    }

    /**
     * 验证处理器注册完整性
     */
    private void validateProcessorRegistration() {
        log.debug("Validating processor registration...");
        
        // 检查是否所有任务类型都有对应的处理器
        Set<TaskType> missingProcessors = new HashSet<>();
        for (TaskType taskType : TaskType.values()) {
            if (!processorMap.containsKey(taskType)) {
                missingProcessors.add(taskType);
            }
        }
        
        if (!missingProcessors.isEmpty()) {
            log.warn("Missing processors for task types: {}", missingProcessors);
            registrationStats.put("missingProcessors", missingProcessors);
        }
        
        // 检查处理器健康状态
        Map<TaskType, Boolean> healthStatus = new HashMap<>();
        for (Map.Entry<TaskType, UniversalTaskProcessor<?>> entry : processorMap.entrySet()) {
            TaskType taskType = entry.getKey();
            UniversalTaskProcessor<?> processor = entry.getValue();
            
            try {
                boolean isHealthy = processor.isHealthy();
                healthStatus.put(taskType, isHealthy);
                
                if (!isHealthy) {
                    log.warn("Processor for task type {} is not healthy", taskType);
                }
            } catch (Exception e) {
                log.error("Failed to check health for processor {}", taskType, e);
                healthStatus.put(taskType, false);
            }
        }
        
        registrationStats.put("healthStatus", healthStatus);
    }

    /**
     * 记录注册摘要信息
     */
    private void logRegistrationSummary() {
        log.info("=== TaskProcessorFactory Registration Summary ===");
        log.info("Total processors registered: {}", processorMap.size());
        log.info("Supported task types: {}", processorMap.keySet());
        
        for (Map.Entry<TaskType, UniversalTaskProcessor<?>> entry : processorMap.entrySet()) {
            TaskType taskType = entry.getKey();
            UniversalTaskProcessor<?> processor = entry.getValue();
            log.info("  {} -> {} (version: {})", 
                    taskType, processor.getClass().getSimpleName(), processor.getVersion());
        }
        
        log.info("Initialization time: {}ms", 
                System.currentTimeMillis() - initializationTime);
        log.info("================================================");
    }

    // ==================== 公共API方法 ====================

    /**
     * 根据任务类型获取处理器
     * 
     * @param taskType 任务类型
     * @return 对应的处理器实例
     * @throws IllegalArgumentException 当任务类型为null时
     * @throws IllegalStateException    当找不到对应处理器时
     */
    @SuppressWarnings("unchecked")
    public <T> UniversalTaskProcessor<T> getProcessor(TaskType taskType) {
        if (taskType == null) {
            throw new IllegalArgumentException("Task type cannot be null");
        }
        
        UniversalTaskProcessor<?> processor = processorMap.get(taskType);
        if (processor == null) {
            throw new IllegalStateException(
                    String.format("No processor found for task type: %s", taskType));
        }
        
        return (UniversalTaskProcessor<T>) processor;
    }

    /**
     * 检查指定任务类型的处理器是否可用
     * 
     * @param taskType 任务类型
     * @return 是否可用
     */
    public boolean isProcessorAvailable(TaskType taskType) {
        if (taskType == null) {
            return false;
        }
        
        UniversalTaskProcessor<?> processor = processorMap.get(taskType);
        if (processor == null) {
            return false;
        }
        
        try {
            return processor.isHealthy();
        } catch (Exception e) {
            log.error("Failed to check processor health for task type {}", taskType, e);
            return false;
        }
    }

    /**
     * 获取所有注册的处理器
     * 
     * @return 处理器映射的只读副本
     */
    public Map<TaskType, UniversalTaskProcessor<?>> getAllProcessors() {
        return Collections.unmodifiableMap(new HashMap<>(processorMap));
    }

    /**
     * 获取所有健康的处理器
     * 
     * @return 健康处理器的映射
     */
    public Map<TaskType, UniversalTaskProcessor<?>> getHealthyProcessors() {
        return processorMap.entrySet().stream()
                .filter(entry -> {
                    try {
                        return entry.getValue().isHealthy();
                    } catch (Exception e) {
                        log.error("Failed to check processor health for task type {}", 
                                entry.getKey(), e);
                        return false;
                    }
                })
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (existing, replacement) -> existing,
                        LinkedHashMap::new
                ));
    }

    /**
     * 获取支持的任务类型列表
     * 
     * @return 支持的任务类型集合
     */
    public Set<TaskType> getSupportedTaskTypes() {
        return Collections.unmodifiableSet(processorMap.keySet());
    }

    /**
     * 获取处理器统计信息
     * 
     * @return 统计信息映射
     */
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        // 基础统计
        stats.put("totalProcessors", processorMap.size());
        stats.put("supportedTaskTypes", processorMap.keySet());
        stats.put("initializationTime", initializationTime);
        
        // 健康状态统计
        long healthyCount = processorMap.values().stream()
                .mapToLong(processor -> {
                    try {
                        return processor.isHealthy() ? 1 : 0;
                    } catch (Exception e) {
                        return 0;
                    }
                })
                .sum();
        
        stats.put("healthyProcessors", healthyCount);
        stats.put("unhealthyProcessors", processorMap.size() - healthyCount);
        
        // 处理器详细信息
        Map<TaskType, Map<String, Object>> processorDetails = new HashMap<>();
        for (Map.Entry<TaskType, UniversalTaskProcessor<?>> entry : processorMap.entrySet()) {
            TaskType taskType = entry.getKey();
            UniversalTaskProcessor<?> processor = entry.getValue();
            
            Map<String, Object> details = new HashMap<>();
            details.put("className", processor.getClass().getSimpleName());
            details.put("version", processor.getVersion());
            details.put("description", processor.getDescription());
            
            try {
                details.put("healthy", processor.isHealthy());
                details.put("configuration", processor.getConfiguration());
                details.put("statistics", processor.getStatistics());
            } catch (Exception e) {
                details.put("healthy", false);
                details.put("error", e.getMessage());
            }
            
            processorDetails.put(taskType, details);
        }
        
        stats.put("processorDetails", processorDetails);
        stats.put("registrationStats", new HashMap<>(registrationStats));
        
        return stats;
    }

    /**
     * 获取工厂状态信息
     * 
     * @return 状态信息
     */
    public Map<String, Object> getStatus() {
        Map<String, Object> status = new HashMap<>();
        
        status.put("initialized", !processorMap.isEmpty());
        status.put("processorCount", processorMap.size());
        status.put("healthyProcessorCount", getHealthyProcessors().size());
        status.put("initializationTime", initializationTime);
        status.put("uptime", System.currentTimeMillis() - initializationTime);
        
        return status;
    }

    /**
     * 刷新处理器缓存
     * 重新扫描和注册所有处理器
     */
    public void refreshCache() {
        log.info("Refreshing processor cache...");

        try {
            // 清空现有缓存
            processorMap.clear();

            // 重新初始化处理器
            afterPropertiesSet();

            log.info("Processor cache refreshed successfully. Total processors: {}", processorMap.size());

        } catch (Exception e) {
            log.error("Failed to refresh processor cache", e);
            throw new RuntimeException("Failed to refresh processor cache", e);
        }
    }
}
