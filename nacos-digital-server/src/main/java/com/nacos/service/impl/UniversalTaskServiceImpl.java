package com.nacos.service.impl;

import com.nacos.entity.dto.CallbackData;
import com.nacos.entity.dto.TaskMessage;
import com.nacos.entity.dto.TaskProcessResult;
import com.nacos.entity.enums.TaskType;
import com.nacos.entity.enums.UnifiedTaskStatusEnum;
import com.nacos.result.Result;
import com.nacos.service.UniversalTaskService;
import com.nacos.service.message.UniversalTaskMessageSender;
import com.nacos.service.message.UniversalTaskQueueHealthChecker;
import com.nacos.service.scheduler.UniversalTaskScheduler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 统一任务服务实现类
 * 
 * 基于UniversalTaskScheduler提供统一的任务管理服务，包括：
 * 1. 任务提交与处理
 * 2. 状态查询与管理
 * 3. 回调处理
 * 4. 统计与监控
 * 
 * 设计特点：
 * 1. 统一入口：所有任务操作的统一服务入口
 * 2. 异步处理：支持消息队列和同步两种处理模式
 * 3. 状态管理：统一的任务状态管理和查询
 * 4. 异常安全：完善的异常处理和错误返回
 * 5. 性能优化：批量操作和缓存机制
 * 
 * <AUTHOR>
 * @since 2025-07-31
 * @version 1.0 - 统一任务服务实现
 */
@Service
@Slf4j
public class UniversalTaskServiceImpl implements UniversalTaskService {

    @Autowired
    private UniversalTaskScheduler taskScheduler;

    @Autowired
    private UniversalTaskMessageSender messageSender;

    @Autowired
    private UniversalTaskQueueHealthChecker queueHealthChecker;

    /**
     * 服务启动时间
     */
    private long startupTime;

    /**
     * 服务统计信息
     */
    private final Map<String, Object> serviceStats = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        startupTime = System.currentTimeMillis();
        initServiceStats();
        log.info("UniversalTaskService initialized successfully");
    }

    /**
     * 初始化服务统计信息
     */
    private void initServiceStats() {
        serviceStats.put("startupTime", startupTime);
        serviceStats.put("totalSubmitted", 0L);
        serviceStats.put("totalProcessed", 0L);
        serviceStats.put("totalSucceeded", 0L);
        serviceStats.put("totalFailed", 0L);
        serviceStats.put("totalCancelled", 0L);
        serviceStats.put("totalRetried", 0L);
    }

    // ==================== 核心任务管理方法 ====================

    @Override
    public Result<Map<String, Object>> submitTask(TaskMessage message) {
        log.info("Submitting task message: {}", message);
        
        try {
            // 参数验证
            if (message == null) {
                return Result.ERROR("任务消息不能为空");
            }
            if (!StringUtils.hasText(message.getTaskId())) {
                return Result.ERROR("任务ID不能为空");
            }
            if (message.getTaskType() == null) {
                return Result.ERROR("任务类型不能为空");
            }

            // 使用统一消息发送器发送任务消息
            boolean sendSuccess = messageSender.sendTaskMessage(message);
            if (!sendSuccess) {
                return Result.ERROR("任务消息发送失败");
            }
            
            // 更新统计信息
            incrementStat("totalSubmitted");
            
            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("taskId", message.getTaskId());
            result.put("taskType", message.getTaskType());
            result.put("topic", message.getTaskType().getRedisTopic());
            result.put("submitTime", LocalDateTime.now());
            result.put("status", "submitted");

            log.info("Task message submitted successfully: taskId={}, topic={}",
                    message.getTaskId(), message.getTaskType().getRedisTopic());

            return Result.SUCCESS(result);

        } catch (Exception e) {
            log.error("Failed to submit task message: {}", message, e);
            return Result.ERROR("任务提交失败：" + e.getMessage());
        }
    }

    @Override
    public Result<TaskProcessResult> processTaskSync(String taskId) {
        log.info("Processing task synchronously: {}", taskId);
        
        try {
            // 参数验证
            if (!StringUtils.hasText(taskId)) {
                return Result.ERROR("任务ID不能为空");
            }

            // 调用调度器处理任务
            TaskProcessResult result = taskScheduler.processTask(taskId);

            // 更新统计信息
            incrementStat("totalProcessed");
            if (result.isSuccess()) {
                incrementStat("totalSucceeded");
            } else {
                incrementStat("totalFailed");
            }

            log.info("Task processed synchronously: taskId={}, success={}",
                    taskId, result.isSuccess());

            return Result.SUCCESS(result);

        } catch (Exception e) {
            log.error("Failed to process task synchronously: {}", taskId, e);
            incrementStat("totalFailed");
            return Result.ERROR("任务处理失败：" + e.getMessage());
        }
    }

    @Override
    public Result<Boolean> handleCallback(String taskId, CallbackData callbackData) {
        log.info("Handling callback for task: {}", taskId);
        
        try {
            // 参数验证
            if (!StringUtils.hasText(taskId)) {
                return Result.ERROR("任务ID不能为空");
            }
            if (callbackData == null) {
                return Result.ERROR("回调数据不能为空");
            }

            // 调用调度器处理回调
            taskScheduler.handleCallback(taskId, callbackData);

            log.info("Callback handled successfully for task: {}", taskId);
            return Result.SUCCESS(true);

        } catch (Exception e) {
            log.error("Failed to handle callback for task: {}", taskId, e);
            return Result.ERROR("回调处理失败：" + e.getMessage());
        }
    }

    @Override
    public Result<Boolean> cancelTask(String taskId, String userId, String reason) {
        log.info("Cancelling task: taskId={}, userId={}, reason={}", taskId, userId, reason);
        
        try {
            // 参数验证
            if (!StringUtils.hasText(taskId)) {
                return Result.ERROR("任务ID不能为空");
            }
            if (!StringUtils.hasText(userId)) {
                return Result.ERROR("用户ID不能为空");
            }

            // 调用调度器取消任务
            boolean cancelled = taskScheduler.cancelTask(taskId);
            
            if (cancelled) {
                incrementStat("totalCancelled");
                log.info("Task cancelled successfully: taskId={}", taskId);
            } else {
                log.warn("Task cancellation failed: taskId={}", taskId);
            }

            return Result.SUCCESS(cancelled);

        } catch (Exception e) {
            log.error("Failed to cancel task: taskId={}", taskId, e);
            return Result.ERROR("任务取消失败：" + e.getMessage());
        }
    }

    // ==================== 任务查询方法 ====================

    @Override
    public Result<Map<String, Object>> getTaskStatus(String taskId) {
        log.debug("Getting task status: {}", taskId);
        
        try {
            // 参数验证
            if (!StringUtils.hasText(taskId)) {
                return Result.ERROR("任务ID不能为空");
            }

            // 调用调度器查询状态
            Map<String, Object> status = taskScheduler.queryTaskStatus(taskId);

            if (status == null || status.isEmpty()) {
                return Result.ERROR("任务不存在或已被删除");
            }

            return Result.SUCCESS(status);

        } catch (Exception e) {
            log.error("Failed to get task status: {}", taskId, e);
            return Result.ERROR("状态查询失败：" + e.getMessage());
        }
    }

    @Override
    public Result<Map<String, Object>> getTaskStatus(String taskId, TaskType taskType) {
        log.debug("Getting task status with type: taskId={}, taskType={}", taskId, taskType);
        
        try {
            // 参数验证
            if (!StringUtils.hasText(taskId)) {
                return Result.ERROR("任务ID不能为空");
            }
            if (taskType == null) {
                return Result.ERROR("任务类型不能为空");
            }

            // 调用调度器查询指定类型的状态
            Map<String, Object> status = taskScheduler.queryTaskStatusByType(taskId, taskType);

            if (status == null || status.isEmpty()) {
                return Result.ERROR("任务不存在或已被删除");
            }

            return Result.SUCCESS(status);

        } catch (Exception e) {
            log.error("Failed to get task status with type: taskId={}, taskType={}",
                     taskId, taskType, e);
            return Result.ERROR("状态查询失败：" + e.getMessage());
        }
    }

    @Override
    public Result<Map<String, Map<String, Object>>> batchGetTaskStatus(List<String> taskIds) {
        log.debug("Batch getting task status for {} tasks", taskIds != null ? taskIds.size() : 0);
        
        try {
            // 参数验证
            if (taskIds == null || taskIds.isEmpty()) {
                return Result.ERROR("任务ID列表不能为空");
            }
            if (taskIds.size() > 100) {
                return Result.ERROR("批量查询任务数量不能超过100个");
            }

            Map<String, Map<String, Object>> results = new HashMap<>();
            
            for (String taskId : taskIds) {
                if (StringUtils.hasText(taskId)) {
                    try {
                        Map<String, Object> status = taskScheduler.queryTaskStatus(taskId);
                        if (status != null && !status.isEmpty()) {
                            results.put(taskId, status);
                        }
                    } catch (Exception e) {
                        log.warn("Failed to get status for task: {}", taskId, e);
                        // 继续处理其他任务，不中断批量操作
                    }
                }
            }

            return Result.SUCCESS(results);

        } catch (Exception e) {
            log.error("Failed to batch get task status", e);
            return Result.ERROR("批量状态查询失败：" + e.getMessage());
        }
    }

    @Override
    public Result<Map<String, Object>> getUserTasks(String userId, TaskType taskType,
                                                   UnifiedTaskStatusEnum status,
                                                   Integer pageNum, Integer pageSize) {
        log.debug("Getting user tasks: userId={}, taskType={}, status={}, page={}/{}",
                 userId, taskType, status, pageNum, pageSize);

        try {
            // 参数验证
            if (!StringUtils.hasText(userId)) {
                return Result.ERROR("用户ID不能为空");
            }
            if (pageNum == null || pageNum < 1) {
                pageNum = 1;
            }
            if (pageSize == null || pageSize < 1) {
                pageSize = 10;
            }
            if (pageSize > 100) {
                return Result.ERROR("每页大小不能超过100");
            }

            // 调用调度器查询用户任务
            Map<String, Object> result = taskScheduler.getUserTasks(userId, taskType, status, pageNum, pageSize);

            return Result.SUCCESS(result);

        } catch (Exception e) {
            log.error("Failed to get user tasks: userId={}", userId, e);
            return Result.ERROR("用户任务查询失败：" + e.getMessage());
        }
    }

    // ==================== 任务统计方法 ====================

    @Override
    public Result<Map<String, Object>> getUserTaskStatistics(String userId) {
        log.debug("Getting user task statistics: {}", userId);

        try {
            // 参数验证
            if (!StringUtils.hasText(userId)) {
                return Result.ERROR("用户ID不能为空");
            }

            // 调用调度器获取用户统计
            Map<String, Object> statistics = taskScheduler.getUserTaskStatistics(userId);

            return Result.SUCCESS(statistics);

        } catch (Exception e) {
            log.error("Failed to get user task statistics: userId={}", userId, e);
            return Result.ERROR("用户统计查询失败：" + e.getMessage());
        }
    }

    @Override
    public Result<Map<String, Object>> getSystemTaskStatistics(TaskType taskType) {
        log.debug("Getting system task statistics: taskType={}", taskType);

        try {
            // 调用调度器获取系统统计
            Map<String, Object> statistics = taskScheduler.getSystemTaskStatistics(taskType);

            return Result.SUCCESS(statistics);

        } catch (Exception e) {
            log.error("Failed to get system task statistics: taskType={}", taskType, e);
            return Result.ERROR("系统统计查询失败：" + e.getMessage());
        }
    }

    // ==================== 任务管理方法 ====================

    @Override
    public Result<Boolean> retryTask(String taskId, String userId) {
        log.info("Retrying task: taskId={}, userId={}", taskId, userId);

        try {
            // 参数验证
            if (!StringUtils.hasText(taskId)) {
                return Result.ERROR("任务ID不能为空");
            }
            if (!StringUtils.hasText(userId)) {
                return Result.ERROR("用户ID不能为空");
            }

            // 调用调度器重试任务
            boolean retried = taskScheduler.retryTask(taskId, userId);

            if (retried) {
                incrementStat("totalRetried");
                log.info("Task retried successfully: taskId={}", taskId);
            } else {
                log.warn("Task retry failed: taskId={}", taskId);
            }

            return Result.SUCCESS(retried);

        } catch (Exception e) {
            log.error("Failed to retry task: taskId={}", taskId, e);
            return Result.ERROR("任务重试失败：" + e.getMessage());
        }
    }

    @Override
    public Result<Map<String, Object>> batchRetryTasks(List<String> taskIds, String userId) {
        log.info("Batch retrying {} tasks for user: {}",
                taskIds != null ? taskIds.size() : 0, userId);

        try {
            // 参数验证
            if (taskIds == null || taskIds.isEmpty()) {
                return Result.ERROR("任务ID列表不能为空");
            }
            if (taskIds.size() > 50) {
                return Result.ERROR("批量重试任务数量不能超过50个");
            }
            if (!StringUtils.hasText(userId)) {
                return Result.ERROR("用户ID不能为空");
            }

            Map<String, Object> result = new HashMap<>();
            int successCount = 0;
            int failedCount = 0;
            List<String> successTasks = new ArrayList<>();
            List<String> failedTasks = new ArrayList<>();

            for (String taskId : taskIds) {
                if (StringUtils.hasText(taskId)) {
                    try {
                        boolean retried = taskScheduler.retryTask(taskId, userId);
                        if (retried) {
                            successCount++;
                            successTasks.add(taskId);
                            incrementStat("totalRetried");
                        } else {
                            failedCount++;
                            failedTasks.add(taskId);
                        }
                    } catch (Exception e) {
                        log.warn("Failed to retry task in batch: {}", taskId, e);
                        failedCount++;
                        failedTasks.add(taskId);
                    }
                }
            }

            result.put("totalCount", taskIds.size());
            result.put("successCount", successCount);
            result.put("failedCount", failedCount);
            result.put("successTasks", successTasks);
            result.put("failedTasks", failedTasks);

            log.info("Batch retry completed: success={}, failed={}", successCount, failedCount);
            return Result.SUCCESS(result);

        } catch (Exception e) {
            log.error("Failed to batch retry tasks", e);
            return Result.ERROR("批量重试失败：" + e.getMessage());
        }
    }

    @Override
    public Result<Map<String, Object>> cleanupExpiredTasks(Integer beforeDays, TaskType taskType) {
        log.info("Cleaning up expired tasks: beforeDays={}, taskType={}", beforeDays, taskType);

        try {
            // 参数验证
            if (beforeDays == null || beforeDays < 1) {
                return Result.ERROR("保留天数必须大于0");
            }
            if (beforeDays > 365) {
                return Result.ERROR("保留天数不能超过365天");
            }

            // 调用调度器清理过期任务
            Map<String, Object> result = taskScheduler.cleanupExpiredTasks(beforeDays, taskType);

            log.info("Expired tasks cleanup completed: {}", result);
            return Result.SUCCESS(result);

        } catch (Exception e) {
            log.error("Failed to cleanup expired tasks", e);
            return Result.ERROR("过期任务清理失败：" + e.getMessage());
        }
    }

    // ==================== 系统管理方法 ====================

    @Override
    public Result<Map<String, Object>> getHealthStatus() {
        log.debug("Getting service health status");

        try {
            Map<String, Object> healthStatus = new HashMap<>();

            // 检查调度器健康状态
            boolean schedulerHealthy = taskScheduler.isHealthy();
            healthStatus.put("scheduler", schedulerHealthy);

            // 检查服务运行时间
            long uptime = System.currentTimeMillis() - startupTime;
            healthStatus.put("uptime", uptime);
            healthStatus.put("startupTime", startupTime);

            // 获取调度器统计信息
            Map<String, Object> schedulerStats = taskScheduler.getStatistics();
            healthStatus.put("schedulerStats", schedulerStats);

            // 服务统计信息
            healthStatus.put("serviceStats", new HashMap<>(serviceStats));

            // 整体健康状态
            boolean overall = schedulerHealthy;
            healthStatus.put("healthy", overall);
            healthStatus.put("status", overall ? "UP" : "DOWN");

            return Result.SUCCESS(healthStatus);

        } catch (Exception e) {
            log.error("Failed to get health status", e);
            return Result.ERROR("健康状态查询失败：" + e.getMessage());
        }
    }

    @Override
    public Result<Map<String, Object>> getServiceStatistics() {
        log.debug("Getting service statistics");

        try {
            Map<String, Object> statistics = new HashMap<>();

            // 服务基本信息
            statistics.put("startupTime", startupTime);
            statistics.put("uptime", System.currentTimeMillis() - startupTime);

            // 服务统计信息
            statistics.put("serviceStats", new HashMap<>(serviceStats));

            // 调度器统计信息
            Map<String, Object> schedulerStats = taskScheduler.getStatistics();
            statistics.put("schedulerStats", schedulerStats);

            // 消息发送统计信息
            Map<String, Object> messageSendingStats = messageSender.getSendingStatistics();
            statistics.put("messageSendingStats", messageSendingStats);

            // 计算成功率
            Long totalProcessed = (Long) serviceStats.getOrDefault("totalProcessed", 0L);
            Long totalSucceeded = (Long) serviceStats.getOrDefault("totalSucceeded", 0L);
            if (totalProcessed > 0) {
                double successRate = (double) totalSucceeded / totalProcessed * 100;
                statistics.put("successRate", String.format("%.2f%%", successRate));
            } else {
                statistics.put("successRate", "N/A");
            }

            return Result.SUCCESS(statistics);

        } catch (Exception e) {
            log.error("Failed to get service statistics", e);
            return Result.ERROR("服务统计查询失败：" + e.getMessage());
        }
    }

    @Override
    public Result<Boolean> refreshProcessorCache() {
        log.info("Refreshing processor cache");

        try {
            // 调用调度器刷新处理器缓存
            boolean refreshed = taskScheduler.refreshProcessorCache();

            if (refreshed) {
                log.info("Processor cache refreshed successfully");
            } else {
                log.warn("Processor cache refresh failed");
            }

            return Result.SUCCESS(refreshed);

        } catch (Exception e) {
            log.error("Failed to refresh processor cache", e);
            return Result.ERROR("处理器缓存刷新失败：" + e.getMessage());
        }
    }

    @Override
    public Result<Map<String, Object>> checkQueueHealth() {
        log.info("Checking queue health");

        try {
            // 调用队列健康检查器
            Map<String, Object> healthReport = queueHealthChecker.checkAllQueuesHealth();

            // 更新统计信息
            incrementStat("queueHealthChecks");

            log.info("Queue health check completed: overallHealthy={}",
                    healthReport.get("overallHealthy"));

            return Result.SUCCESS(healthReport);

        } catch (Exception e) {
            log.error("Failed to check queue health", e);
            incrementStat("queueHealthCheckErrors");
            return Result.ERROR("队列健康检查失败：" + e.getMessage());
        }
    }

    /**
     * 增加统计计数
     */
    private void incrementStat(String key) {
        serviceStats.compute(key, (k, v) -> {
            if (v instanceof Long) {
                return ((Long) v) + 1;
            }
            return 1L;
        });
    }
}
