package com.nacos.service.message;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.nacos.entity.dto.TaskMessage;
import com.nacos.entity.enums.TaskType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

/**
 * 统一任务消息发送器
 * 
 * 负责向Redis队列发送任务消息，包括：
 * 1. 任务消息序列化和发送
 * 2. 队列路由和负载均衡
 * 3. 发送状态监控和统计
 * 4. 异常处理和重试机制
 * 
 * 设计原则：
 * 1. 类型安全：严格的消息格式和类型检查
 * 2. 队列隔离：根据任务类型自动路由到对应队列
 * 3. 可靠性：完整的异常处理和发送确认
 * 4. 可观测性：详细的发送统计和监控支持
 * 
 * <AUTHOR>
 * @since 2025-07-31
 * @version 1.0 - 统一任务消息发送架构
 */
@Service
@Slf4j
public class UniversalTaskMessageSender {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 消息发送统计
     */
    private final Map<String, Object> sendingStats = new ConcurrentHashMap<>();

    /**
     * 服务启动时间
     */
    private final long startupTime = System.currentTimeMillis();

    /**
     * 发送任务消息到Redis队列
     * 
     * @param taskMessage 任务消息对象
     * @return 是否发送成功
     */
    public boolean sendTaskMessage(TaskMessage taskMessage) {
        if (taskMessage == null) {
            log.error("Cannot send null task message");
            return false;
        }

        try {
            // 1. 验证消息完整性
            if (!validateTaskMessage(taskMessage)) {
                incrementStat("validationFailures");
                return false;
            }

            // 2. 获取目标队列
            String targetTopic = getTargetTopic(taskMessage.getTaskType());
            if (targetTopic == null) {
                log.error("Cannot determine target topic for task type: {}", taskMessage.getTaskType());
                incrementStat("topicResolutionFailures");
                return false;
            }

            // 3. 序列化消息
            String messageJson = serializeTaskMessage(taskMessage);
            if (messageJson == null) {
                incrementStat("serializationFailures");
                return false;
            }

            // 4. 发送到Redis队列
            stringRedisTemplate.convertAndSend(targetTopic, messageJson);

            // 5. 更新统计信息
            incrementStat("totalSent");
            incrementStat(taskMessage.getTaskType().getCode().toLowerCase() + "Sent");

            log.info("Task message sent successfully: taskId={}, taskType={}, topic={}", 
                    taskMessage.getTaskId(), taskMessage.getTaskType(), targetTopic);

            return true;

        } catch (Exception e) {
            log.error("Failed to send task message: taskId={}, taskType={}", 
                    taskMessage.getTaskId(), taskMessage.getTaskType(), e);
            incrementStat("sendFailures");
            return false;
        }
    }

    /**
     * 批量发送任务消息
     * 
     * @param taskMessages 任务消息列表
     * @return 发送成功的消息数量
     */
    public int sendTaskMessages(TaskMessage... taskMessages) {
        if (taskMessages == null || taskMessages.length == 0) {
            log.warn("No task messages to send");
            return 0;
        }

        int successCount = 0;
        for (TaskMessage taskMessage : taskMessages) {
            if (sendTaskMessage(taskMessage)) {
                successCount++;
            }
        }

        log.info("Batch send completed: total={}, success={}, failed={}", 
                taskMessages.length, successCount, taskMessages.length - successCount);

        return successCount;
    }

    /**
     * 验证任务消息完整性
     * 
     * @param taskMessage 任务消息对象
     * @return 验证是否通过
     */
    private boolean validateTaskMessage(TaskMessage taskMessage) {
        // 检查必要字段
        if (taskMessage.getTaskId() == null || taskMessage.getTaskId().trim().isEmpty()) {
            log.error("Task message missing taskId");
            return false;
        }

        if (taskMessage.getTaskType() == null) {
            log.error("Task message missing taskType: taskId={}", taskMessage.getTaskId());
            return false;
        }

        if (taskMessage.getUserId() == null || taskMessage.getUserId().trim().isEmpty()) {
            log.error("Task message missing userId: taskId={}", taskMessage.getTaskId());
            return false;
        }

        return true;
    }

    /**
     * 获取目标队列主题
     * 
     * @param taskType 任务类型
     * @return Redis队列主题名称
     */
    private String getTargetTopic(TaskType taskType) {
        if (taskType == null) {
            return null;
        }
        return taskType.getRedisTopic();
    }

    /**
     * 序列化任务消息
     * 
     * @param taskMessage 任务消息对象
     * @return JSON字符串，序列化失败返回null
     */
    private String serializeTaskMessage(TaskMessage taskMessage) {
        try {
            // 设置发送时间戳
            if (taskMessage.getSendTime() == null) {
                taskMessage.setSendTime(LocalDateTime.now());
            }

            return objectMapper.writeValueAsString(taskMessage);

        } catch (Exception e) {
            log.error("Failed to serialize task message: taskId={}, taskType={}", 
                    taskMessage.getTaskId(), taskMessage.getTaskType(), e);
            return null;
        }
    }

    /**
     * 增加统计计数
     * 
     * @param key 统计键
     */
    private void incrementStat(String key) {
        sendingStats.merge(key, 1L, (oldValue, newValue) -> 
                ((Number) oldValue).longValue() + ((Number) newValue).longValue());
    }

    /**
     * 获取发送统计信息
     * 
     * @return 统计信息
     */
    public Map<String, Object> getSendingStatistics() {
        Map<String, Object> stats = new ConcurrentHashMap<>(sendingStats);
        stats.put("startupTime", startupTime);
        stats.put("uptime", System.currentTimeMillis() - startupTime);
        return stats;
    }

    /**
     * 重置统计信息
     */
    public void resetStatistics() {
        sendingStats.clear();
        log.info("Sending statistics reset");
    }
}
