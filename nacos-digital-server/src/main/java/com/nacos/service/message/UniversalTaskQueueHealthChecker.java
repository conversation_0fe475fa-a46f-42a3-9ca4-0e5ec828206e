package com.nacos.service.message;

import com.nacos.entity.enums.TaskType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 统一任务队列健康检查器
 * 
 * 负责监控Redis消息队列的健康状态，包括：
 * 1. 队列连接状态检查
 * 2. 消息发送和接收测试
 * 3. 队列性能监控
 * 4. 异常情况报告
 * 
 * 设计原则：
 * 1. 主动监控：定期检查队列健康状态
 * 2. 快速诊断：提供详细的健康状态报告
 * 3. 性能监控：监控队列吞吐量和延迟
 * 4. 故障预警：及时发现和报告异常情况
 * 
 * <AUTHOR>
 * @since 2025-07-31
 * @version 1.0 - 统一任务队列健康监控
 */
@Service
@Slf4j
public class UniversalTaskQueueHealthChecker {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 健康检查统计
     */
    private final Map<String, Object> healthStats = new ConcurrentHashMap<>();

    /**
     * 服务启动时间
     */
    private final long startupTime = System.currentTimeMillis();

    /**
     * 检查所有任务队列的健康状态
     * 
     * @return 健康状态报告
     */
    public Map<String, Object> checkAllQueuesHealth() {
        log.debug("Checking all task queues health");

        Map<String, Object> healthReport = new HashMap<>();
        
        try {
            // 检查Redis连接状态
            boolean redisConnected = checkRedisConnection();
            healthReport.put("redisConnected", redisConnected);

            // 检查各个队列状态
            Map<String, Object> queueStatuses = new HashMap<>();
            for (TaskType taskType : TaskType.values()) {
                Map<String, Object> queueHealth = checkQueueHealth(taskType);
                queueStatuses.put(taskType.getCode(), queueHealth);
            }
            healthReport.put("queueStatuses", queueStatuses);

            // 整体健康状态
            boolean overallHealthy = redisConnected && isAllQueuesHealthy(queueStatuses);
            healthReport.put("overallHealthy", overallHealthy);

            // 统计信息
            healthReport.put("healthStats", new HashMap<>(healthStats));
            healthReport.put("checkTime", LocalDateTime.now());
            healthReport.put("uptime", System.currentTimeMillis() - startupTime);

            // 更新统计
            incrementStat("totalHealthChecks");
            if (overallHealthy) {
                incrementStat("healthyChecks");
            } else {
                incrementStat("unhealthyChecks");
            }

            log.debug("Health check completed: overallHealthy={}", overallHealthy);
            return healthReport;

        } catch (Exception e) {
            log.error("Failed to check queues health", e);
            incrementStat("healthCheckErrors");
            
            healthReport.put("overallHealthy", false);
            healthReport.put("error", e.getMessage());
            healthReport.put("checkTime", LocalDateTime.now());
            
            return healthReport;
        }
    }

    /**
     * 检查特定队列的健康状态
     * 
     * @param taskType 任务类型
     * @return 队列健康状态
     */
    public Map<String, Object> checkQueueHealth(TaskType taskType) {
        Map<String, Object> queueHealth = new HashMap<>();
        
        try {
            String topic = taskType.getRedisTopic();
            
            // 检查队列配置
            boolean configValid = topic != null && !topic.trim().isEmpty();
            queueHealth.put("configValid", configValid);
            queueHealth.put("topic", topic);
            
            if (!configValid) {
                queueHealth.put("healthy", false);
                queueHealth.put("error", "Invalid queue configuration");
                return queueHealth;
            }

            // 检查队列可达性（尝试发送测试消息）
            boolean reachable = testQueueReachability(topic);
            queueHealth.put("reachable", reachable);

            // 整体健康状态
            boolean healthy = configValid && reachable;
            queueHealth.put("healthy", healthy);
            queueHealth.put("taskType", taskType.getCode());
            queueHealth.put("description", taskType.getDescription());

            return queueHealth;

        } catch (Exception e) {
            log.error("Failed to check queue health for {}", taskType, e);
            queueHealth.put("healthy", false);
            queueHealth.put("error", e.getMessage());
            return queueHealth;
        }
    }

    /**
     * 检查Redis连接状态
     * 
     * @return 连接是否正常
     */
    private boolean checkRedisConnection() {
        try {
            // 执行简单的ping命令测试连接
            String pong = stringRedisTemplate.getConnectionFactory()
                    .getConnection()
                    .ping();
            
            boolean connected = "PONG".equals(pong);
            log.debug("Redis connection check: connected={}", connected);
            return connected;

        } catch (Exception e) {
            log.error("Redis connection check failed", e);
            return false;
        }
    }

    /**
     * 测试队列可达性
     * 
     * @param topic 队列主题
     * @return 队列是否可达
     */
    private boolean testQueueReachability(String topic) {
        try {
            // 发送测试消息（不会被实际处理）
            String testMessage = "{\"test\":true,\"timestamp\":" + System.currentTimeMillis() + "}";
            stringRedisTemplate.convertAndSend(topic + "_health_check", testMessage);
            
            log.debug("Queue reachability test passed for topic: {}", topic);
            return true;

        } catch (Exception e) {
            log.error("Queue reachability test failed for topic: {}", topic, e);
            return false;
        }
    }

    /**
     * 检查所有队列是否健康
     * 
     * @param queueStatuses 队列状态映射
     * @return 是否所有队列都健康
     */
    private boolean isAllQueuesHealthy(Map<String, Object> queueStatuses) {
        for (Object statusObj : queueStatuses.values()) {
            if (statusObj instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> status = (Map<String, Object>) statusObj;
                Boolean healthy = (Boolean) status.get("healthy");
                if (healthy == null || !healthy) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 增加统计计数
     * 
     * @param key 统计键
     */
    private void incrementStat(String key) {
        healthStats.merge(key, 1L, (oldValue, newValue) -> 
                ((Number) oldValue).longValue() + ((Number) newValue).longValue());
    }

    /**
     * 获取健康检查统计信息
     * 
     * @return 统计信息
     */
    public Map<String, Object> getHealthStatistics() {
        Map<String, Object> stats = new HashMap<>(healthStats);
        stats.put("startupTime", startupTime);
        stats.put("uptime", System.currentTimeMillis() - startupTime);
        
        // 计算健康率
        Long totalChecks = (Long) healthStats.getOrDefault("totalHealthChecks", 0L);
        Long healthyChecks = (Long) healthStats.getOrDefault("healthyChecks", 0L);
        if (totalChecks > 0) {
            double healthRate = (double) healthyChecks / totalChecks * 100;
            stats.put("healthRate", String.format("%.2f%%", healthRate));
        } else {
            stats.put("healthRate", "N/A");
        }
        
        return stats;
    }

    /**
     * 重置健康检查统计
     */
    public void resetHealthStatistics() {
        healthStats.clear();
        log.info("Health check statistics reset");
    }
}
