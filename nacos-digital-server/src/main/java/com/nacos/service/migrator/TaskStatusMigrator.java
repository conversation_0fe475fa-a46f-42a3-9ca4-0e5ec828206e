package com.nacos.service.migrator;

import com.nacos.entity.enums.*;
import com.nacos.entity.enums.TaskType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 任务状态映射器
 * 
 * 负责处理现有数据库状态与新统一状态的映射转换，确保向后兼容性，支持渐进式迁移。
 * 
 * 主要功能：
 * 1. 现有状态到统一状态的映射转换
 * 2. 统一状态到现有状态的反向映射
 * 3. 状态兼容性检查和验证
 * 4. 迁移过程中的状态同步
 * 5. 多种任务类型的状态映射支持
 * 
 * 设计原则：
 * 1. 向后兼容：确保现有系统功能不受影响
 * 2. 渐进迁移：支持新旧系统并存运行
 * 3. 状态一致性：保证状态映射的准确性和一致性
 * 4. 扩展性：支持新增任务类型和状态
 * 5. 可追溯性：记录状态转换过程和原因
 * 
 * 状态映射策略：
 * 1. 视频翻译任务：VideoTranslateStatusEnum → UnifiedTaskStatusEnum
 * 2. 音频生成任务：AudioTaskStatusEnum → UnifiedTaskStatusEnum  
 * 3. 视频编辑任务：VideoEditTaskStatusEnum → UnifiedTaskStatusEnum
 * 4. 通用视频任务：VideoTaskStatusEnum → UnifiedTaskStatusEnum
 * 
 * <AUTHOR>
 * @since 2025-07-31
 * @version 1.0 - 统一任务状态映射架构
 */
@Component
@Slf4j
public class TaskStatusMigrator {

    /**
     * 视频翻译状态映射表：现有状态 → 统一状态
     */
    private static final Map<String, UnifiedTaskStatusEnum> VIDEO_TRANSLATE_STATUS_MAP = new HashMap<>();
    
    /**
     * 音频生成状态映射表：现有状态 → 统一状态
     */
    private static final Map<Integer, UnifiedTaskStatusEnum> AUDIO_GENERATION_STATUS_MAP = new HashMap<>();
    
    /**
     * 视频编辑状态映射表：现有状态 → 统一状态
     */
    private static final Map<Integer, UnifiedTaskStatusEnum> VIDEO_EDIT_STATUS_MAP = new HashMap<>();
    
    /**
     * 通用视频任务状态映射表：现有状态 → 统一状态
     */
    private static final Map<Integer, UnifiedTaskStatusEnum> VIDEO_TASK_STATUS_MAP = new HashMap<>();

    /**
     * 反向映射表：统一状态 → 视频翻译状态
     */
    private static final Map<UnifiedTaskStatusEnum, String> UNIFIED_TO_VIDEO_TRANSLATE_MAP = new HashMap<>();
    
    /**
     * 反向映射表：统一状态 → 音频生成状态
     */
    private static final Map<UnifiedTaskStatusEnum, Integer> UNIFIED_TO_AUDIO_GENERATION_MAP = new HashMap<>();
    
    /**
     * 反向映射表：统一状态 → 视频编辑状态
     */
    private static final Map<UnifiedTaskStatusEnum, Integer> UNIFIED_TO_VIDEO_EDIT_MAP = new HashMap<>();
    
    /**
     * 反向映射表：统一状态 → 通用视频任务状态
     */
    private static final Map<UnifiedTaskStatusEnum, Integer> UNIFIED_TO_VIDEO_TASK_MAP = new HashMap<>();

    static {
        initializeVideoTranslateStatusMapping();
        initializeAudioGenerationStatusMapping();
        initializeVideoEditStatusMapping();
        initializeVideoTaskStatusMapping();
        initializeReverseMappings();
    }

    // ==================== 视频翻译状态映射 ====================

    /**
     * 初始化视频翻译状态映射
     */
    private static void initializeVideoTranslateStatusMapping() {
        // 基于VideoTranslateStatusEnum的状态映射
        VIDEO_TRANSLATE_STATUS_MAP.put("submitted", UnifiedTaskStatusEnum.QUEUING);    // 已提交 → 排队中
        VIDEO_TRANSLATE_STATUS_MAP.put("waiting", UnifiedTaskStatusEnum.QUEUING);      // 等待中 → 排队中
        VIDEO_TRANSLATE_STATUS_MAP.put("preparing", UnifiedTaskStatusEnum.PROGRESS);   // 准备中 → 进行中
        VIDEO_TRANSLATE_STATUS_MAP.put("processing", UnifiedTaskStatusEnum.PROGRESS);  // 处理中 → 进行中
        VIDEO_TRANSLATE_STATUS_MAP.put("completed", UnifiedTaskStatusEnum.SUCCESS);    // 已完成 → 成功
        VIDEO_TRANSLATE_STATUS_MAP.put("failed", UnifiedTaskStatusEnum.FAILED);        // 失败 → 失败
        VIDEO_TRANSLATE_STATUS_MAP.put("cancelled", UnifiedTaskStatusEnum.CANCELLED);  // 已取消 → 已取消
        VIDEO_TRANSLATE_STATUS_MAP.put("timeout", UnifiedTaskStatusEnum.TIMEOUT);      // 超时 → 超时
    }

    /**
     * 视频翻译状态转换：现有状态 → 统一状态
     */
    public UnifiedTaskStatusEnum mapVideoTranslateStatus(String originalStatus) {
        if (originalStatus == null || originalStatus.trim().isEmpty()) {
            log.warn("视频翻译状态为空，默认返回排队中状态");
            return UnifiedTaskStatusEnum.QUEUING;
        }
        
        UnifiedTaskStatusEnum unifiedStatus = VIDEO_TRANSLATE_STATUS_MAP.get(originalStatus.toLowerCase());
        if (unifiedStatus == null) {
            log.warn("未知的视频翻译状态: {}, 默认返回排队中状态", originalStatus);
            return UnifiedTaskStatusEnum.QUEUING;
        }
        
        log.debug("视频翻译状态映射: {} → {}", originalStatus, unifiedStatus);
        return unifiedStatus;
    }

    // ==================== 音频生成状态映射 ====================

    /**
     * 初始化音频生成状态映射
     */
    private static void initializeAudioGenerationStatusMapping() {
        // 基于AudioTaskStatusEnum的状态映射
        AUDIO_GENERATION_STATUS_MAP.put(0, UnifiedTaskStatusEnum.QUEUING);    // 排队中 → 排队中
        AUDIO_GENERATION_STATUS_MAP.put(1, UnifiedTaskStatusEnum.PROGRESS);   // 进行中 → 进行中
        AUDIO_GENERATION_STATUS_MAP.put(2, UnifiedTaskStatusEnum.SUCCESS);    // 生成成功 → 成功
        AUDIO_GENERATION_STATUS_MAP.put(3, UnifiedTaskStatusEnum.FAILED);     // 失败 → 失败
        AUDIO_GENERATION_STATUS_MAP.put(4, UnifiedTaskStatusEnum.TIMEOUT);    // 超时 → 超时
        AUDIO_GENERATION_STATUS_MAP.put(5, UnifiedTaskStatusEnum.CANCELLED);  // 已取消 → 已取消
    }

    /**
     * 音频生成状态转换：现有状态 → 统一状态
     */
    public UnifiedTaskStatusEnum mapAudioGenerationStatus(Integer originalStatus) {
        if (originalStatus == null) {
            log.warn("音频生成状态为空，默认返回排队中状态");
            return UnifiedTaskStatusEnum.QUEUING;
        }
        
        UnifiedTaskStatusEnum unifiedStatus = AUDIO_GENERATION_STATUS_MAP.get(originalStatus);
        if (unifiedStatus == null) {
            log.warn("未知的音频生成状态: {}, 默认返回排队中状态", originalStatus);
            return UnifiedTaskStatusEnum.QUEUING;
        }
        
        log.debug("音频生成状态映射: {} → {}", originalStatus, unifiedStatus);
        return unifiedStatus;
    }

    // ==================== 视频编辑状态映射 ====================

    /**
     * 初始化视频编辑状态映射
     */
    private static void initializeVideoEditStatusMapping() {
        // 基于VideoEditTaskStatusEnum的状态映射
        VIDEO_EDIT_STATUS_MAP.put(0, UnifiedTaskStatusEnum.QUEUING);    // 排队中 → 排队中
        VIDEO_EDIT_STATUS_MAP.put(1, UnifiedTaskStatusEnum.PROGRESS);   // 进行中 → 进行中
        VIDEO_EDIT_STATUS_MAP.put(2, UnifiedTaskStatusEnum.PROGRESS);   // 处理中 → 进行中
        VIDEO_EDIT_STATUS_MAP.put(3, UnifiedTaskStatusEnum.SUCCESS);    // 编辑成功 → 成功
        VIDEO_EDIT_STATUS_MAP.put(4, UnifiedTaskStatusEnum.FAILED);     // 失败 → 失败
        VIDEO_EDIT_STATUS_MAP.put(5, UnifiedTaskStatusEnum.TIMEOUT);    // 超时 → 超时
        VIDEO_EDIT_STATUS_MAP.put(6, UnifiedTaskStatusEnum.CANCELLED);  // 已取消 → 已取消
    }

    /**
     * 视频编辑状态转换：现有状态 → 统一状态
     */
    public UnifiedTaskStatusEnum mapVideoEditStatus(Integer originalStatus) {
        if (originalStatus == null) {
            log.warn("视频编辑状态为空，默认返回排队中状态");
            return UnifiedTaskStatusEnum.QUEUING;
        }
        
        UnifiedTaskStatusEnum unifiedStatus = VIDEO_EDIT_STATUS_MAP.get(originalStatus);
        if (unifiedStatus == null) {
            log.warn("未知的视频编辑状态: {}, 默认返回排队中状态", originalStatus);
            return UnifiedTaskStatusEnum.QUEUING;
        }
        
        log.debug("视频编辑状态映射: {} → {}", originalStatus, unifiedStatus);
        return unifiedStatus;
    }

    // ==================== 通用视频任务状态映射 ====================

    /**
     * 初始化通用视频任务状态映射
     */
    private static void initializeVideoTaskStatusMapping() {
        // 基于VideoTaskStatusEnum的状态映射
        VIDEO_TASK_STATUS_MAP.put(0, UnifiedTaskStatusEnum.QUEUING);    // 排队中 → 排队中
        VIDEO_TASK_STATUS_MAP.put(1, UnifiedTaskStatusEnum.PROGRESS);   // 进行中 → 进行中
        VIDEO_TASK_STATUS_MAP.put(2, UnifiedTaskStatusEnum.PROGRESS);   // 视频生成成功 → 进行中（还需要合并）
        VIDEO_TASK_STATUS_MAP.put(3, UnifiedTaskStatusEnum.SUCCESS);    // 视频合并成功 → 成功
        VIDEO_TASK_STATUS_MAP.put(4, UnifiedTaskStatusEnum.FAILED);     // 失败 → 失败
        VIDEO_TASK_STATUS_MAP.put(5, UnifiedTaskStatusEnum.TIMEOUT);    // 超时 → 超时
    }

    /**
     * 通用视频任务状态转换：现有状态 → 统一状态
     */
    public UnifiedTaskStatusEnum mapVideoTaskStatus(Integer originalStatus) {
        if (originalStatus == null) {
            log.warn("通用视频任务状态为空，默认返回排队中状态");
            return UnifiedTaskStatusEnum.QUEUING;
        }
        
        UnifiedTaskStatusEnum unifiedStatus = VIDEO_TASK_STATUS_MAP.get(originalStatus);
        if (unifiedStatus == null) {
            log.warn("未知的通用视频任务状态: {}, 默认返回排队中状态", originalStatus);
            return UnifiedTaskStatusEnum.QUEUING;
        }
        
        log.debug("通用视频任务状态映射: {} → {}", originalStatus, unifiedStatus);
        return unifiedStatus;
    }

    // ==================== 反向映射初始化 ====================

    /**
     * 初始化反向映射表
     */
    private static void initializeReverseMappings() {
        // 视频翻译反向映射
        UNIFIED_TO_VIDEO_TRANSLATE_MAP.put(UnifiedTaskStatusEnum.QUEUING, "submitted");
        UNIFIED_TO_VIDEO_TRANSLATE_MAP.put(UnifiedTaskStatusEnum.PROGRESS, "processing");
        UNIFIED_TO_VIDEO_TRANSLATE_MAP.put(UnifiedTaskStatusEnum.SUCCESS, "completed");
        UNIFIED_TO_VIDEO_TRANSLATE_MAP.put(UnifiedTaskStatusEnum.FAILED, "failed");
        UNIFIED_TO_VIDEO_TRANSLATE_MAP.put(UnifiedTaskStatusEnum.TIMEOUT, "timeout");
        UNIFIED_TO_VIDEO_TRANSLATE_MAP.put(UnifiedTaskStatusEnum.CANCELLED, "cancelled");

        // 音频生成反向映射
        UNIFIED_TO_AUDIO_GENERATION_MAP.put(UnifiedTaskStatusEnum.QUEUING, 0);
        UNIFIED_TO_AUDIO_GENERATION_MAP.put(UnifiedTaskStatusEnum.PROGRESS, 1);
        UNIFIED_TO_AUDIO_GENERATION_MAP.put(UnifiedTaskStatusEnum.SUCCESS, 2);
        UNIFIED_TO_AUDIO_GENERATION_MAP.put(UnifiedTaskStatusEnum.FAILED, 3);
        UNIFIED_TO_AUDIO_GENERATION_MAP.put(UnifiedTaskStatusEnum.TIMEOUT, 4);
        UNIFIED_TO_AUDIO_GENERATION_MAP.put(UnifiedTaskStatusEnum.CANCELLED, 5);

        // 视频编辑反向映射
        UNIFIED_TO_VIDEO_EDIT_MAP.put(UnifiedTaskStatusEnum.QUEUING, 0);
        UNIFIED_TO_VIDEO_EDIT_MAP.put(UnifiedTaskStatusEnum.PROGRESS, 1);
        UNIFIED_TO_VIDEO_EDIT_MAP.put(UnifiedTaskStatusEnum.SUCCESS, 3);
        UNIFIED_TO_VIDEO_EDIT_MAP.put(UnifiedTaskStatusEnum.FAILED, 4);
        UNIFIED_TO_VIDEO_EDIT_MAP.put(UnifiedTaskStatusEnum.TIMEOUT, 5);
        UNIFIED_TO_VIDEO_EDIT_MAP.put(UnifiedTaskStatusEnum.CANCELLED, 6);

        // 通用视频任务反向映射
        UNIFIED_TO_VIDEO_TASK_MAP.put(UnifiedTaskStatusEnum.QUEUING, 0);
        UNIFIED_TO_VIDEO_TASK_MAP.put(UnifiedTaskStatusEnum.PROGRESS, 1);
        UNIFIED_TO_VIDEO_TASK_MAP.put(UnifiedTaskStatusEnum.SUCCESS, 3);
        UNIFIED_TO_VIDEO_TASK_MAP.put(UnifiedTaskStatusEnum.FAILED, 4);
        UNIFIED_TO_VIDEO_TASK_MAP.put(UnifiedTaskStatusEnum.TIMEOUT, 5);
        UNIFIED_TO_VIDEO_TASK_MAP.put(UnifiedTaskStatusEnum.CANCELLED, 4); // 通用视频任务没有取消状态，映射为失败
    }

    // ==================== 反向映射方法 ====================

    /**
     * 统一状态转换为视频翻译状态
     */
    public String mapToVideoTranslateStatus(UnifiedTaskStatusEnum unifiedStatus) {
        if (unifiedStatus == null) {
            log.warn("统一状态为空，默认返回submitted状态");
            return "submitted";
        }

        String originalStatus = UNIFIED_TO_VIDEO_TRANSLATE_MAP.get(unifiedStatus);
        if (originalStatus == null) {
            log.warn("无法映射统一状态到视频翻译状态: {}, 默认返回submitted", unifiedStatus);
            return "submitted";
        }

        log.debug("统一状态反向映射到视频翻译: {} → {}", unifiedStatus, originalStatus);
        return originalStatus;
    }

    /**
     * 统一状态转换为音频生成状态
     */
    public Integer mapToAudioGenerationStatus(UnifiedTaskStatusEnum unifiedStatus) {
        if (unifiedStatus == null) {
            log.warn("统一状态为空，默认返回排队中状态(0)");
            return 0;
        }

        Integer originalStatus = UNIFIED_TO_AUDIO_GENERATION_MAP.get(unifiedStatus);
        if (originalStatus == null) {
            log.warn("无法映射统一状态到音频生成状态: {}, 默认返回排队中状态(0)", unifiedStatus);
            return 0;
        }

        log.debug("统一状态反向映射到音频生成: {} → {}", unifiedStatus, originalStatus);
        return originalStatus;
    }

    /**
     * 统一状态转换为视频编辑状态
     */
    public Integer mapToVideoEditStatus(UnifiedTaskStatusEnum unifiedStatus) {
        if (unifiedStatus == null) {
            log.warn("统一状态为空，默认返回排队中状态(0)");
            return 0;
        }

        Integer originalStatus = UNIFIED_TO_VIDEO_EDIT_MAP.get(unifiedStatus);
        if (originalStatus == null) {
            log.warn("无法映射统一状态到视频编辑状态: {}, 默认返回排队中状态(0)", unifiedStatus);
            return 0;
        }

        log.debug("统一状态反向映射到视频编辑: {} → {}", unifiedStatus, originalStatus);
        return originalStatus;
    }

    /**
     * 统一状态转换为通用视频任务状态
     */
    public Integer mapToVideoTaskStatus(UnifiedTaskStatusEnum unifiedStatus) {
        if (unifiedStatus == null) {
            log.warn("统一状态为空，默认返回排队中状态(0)");
            return 0;
        }

        Integer originalStatus = UNIFIED_TO_VIDEO_TASK_MAP.get(unifiedStatus);
        if (originalStatus == null) {
            log.warn("无法映射统一状态到通用视频任务状态: {}, 默认返回排队中状态(0)", unifiedStatus);
            return 0;
        }

        log.debug("统一状态反向映射到通用视频任务: {} → {}", unifiedStatus, originalStatus);
        return originalStatus;
    }

    // ==================== 状态兼容性检查 ====================

    /**
     * 检查状态转换的兼容性
     */
    public boolean isStatusTransitionCompatible(TaskType taskType, Object fromStatus, UnifiedTaskStatusEnum toStatus) {
        if (taskType == null || toStatus == null) {
            return false;
        }

        try {
            UnifiedTaskStatusEnum fromUnifiedStatus = mapToUnifiedStatus(taskType, fromStatus);
            return fromUnifiedStatus.canTransitionTo(toStatus);
        } catch (Exception e) {
            log.error("状态转换兼容性检查失败: taskType={}, fromStatus={}, toStatus={}",
                     taskType, fromStatus, toStatus, e);
            return false;
        }
    }

    /**
     * 根据任务类型映射到统一状态
     */
    public UnifiedTaskStatusEnum mapToUnifiedStatus(TaskType taskType, Object originalStatus) {
        if (taskType == null || originalStatus == null) {
            return UnifiedTaskStatusEnum.QUEUING;
        }

        switch (taskType) {
            case VIDEO_TRANSLATE:
                return mapVideoTranslateStatus(originalStatus.toString());
            case AUDIO_GENERATE:
                return mapAudioGenerationStatus((Integer) originalStatus);
            case VIDEO_EDIT:
                return mapVideoEditStatus((Integer) originalStatus);
            default:
                log.warn("未支持的任务类型: {}, 默认返回排队中状态", taskType);
                return UnifiedTaskStatusEnum.QUEUING;
        }
    }

    /**
     * 根据任务类型从统一状态映射回原始状态
     */
    public Object mapFromUnifiedStatus(TaskType taskType, UnifiedTaskStatusEnum unifiedStatus) {
        if (taskType == null || unifiedStatus == null) {
            return null;
        }

        switch (taskType) {
            case VIDEO_TRANSLATE:
                return mapToVideoTranslateStatus(unifiedStatus);
            case AUDIO_GENERATE:
                return mapToAudioGenerationStatus(unifiedStatus);
            case VIDEO_EDIT:
                return mapToVideoEditStatus(unifiedStatus);
            default:
                log.warn("未支持的任务类型: {}", taskType);
                return null;
        }
    }

    // ==================== 迁移辅助方法 ====================

    /**
     * 获取所有支持的任务类型
     */
    public TaskType[] getSupportedTaskTypes() {
        return new TaskType[]{
            TaskType.VIDEO_TRANSLATE,
            TaskType.AUDIO_GENERATE,
            TaskType.VIDEO_EDIT
        };
    }

    /**
     * 检查任务类型是否支持状态映射
     */
    public boolean isTaskTypeSupported(TaskType taskType) {
        if (taskType == null) {
            return false;
        }

        for (TaskType supportedType : getSupportedTaskTypes()) {
            if (supportedType == taskType) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取映射统计信息
     */
    public Map<String, Object> getMappingStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("videoTranslateMappings", VIDEO_TRANSLATE_STATUS_MAP.size());
        stats.put("audioGenerationMappings", AUDIO_GENERATION_STATUS_MAP.size());
        stats.put("videoEditMappings", VIDEO_EDIT_STATUS_MAP.size());
        stats.put("videoTaskMappings", VIDEO_TASK_STATUS_MAP.size());
        stats.put("supportedTaskTypes", getSupportedTaskTypes().length);
        stats.put("unifiedStatusCount", UnifiedTaskStatusEnum.values().length);
        return stats;
    }
}
