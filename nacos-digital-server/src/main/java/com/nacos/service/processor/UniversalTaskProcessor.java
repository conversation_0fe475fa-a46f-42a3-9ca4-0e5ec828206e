package com.nacos.service.processor;

import com.nacos.entity.context.TaskContext;
import com.nacos.entity.dto.CallbackData;
import com.nacos.entity.dto.TaskProcessResult;
import com.nacos.entity.enums.TaskType;

/**
 * 统一任务处理器接口
 * 
 * 定义所有任务处理器必须实现的核心方法，包括：
 * 1. 任务处理的主要逻辑
 * 2. 异步回调处理
 * 3. 任务类型标识
 * 4. 健康检查和状态查询
 * 
 * 设计原则：
 * 1. 职责单一：每个处理器只处理一种任务类型
 * 2. 无状态设计：处理器本身不保存任务状态
 * 3. 异常安全：所有异常都应该被捕获并转换为结果对象
 * 4. 可测试性：接口设计便于单元测试
 * 
 * 实现要求：
 * 1. 必须使用@Component注解标记为Spring Bean
 * 2. 必须实现所有接口方法
 * 3. 处理器Bean名称应遵循命名规范：{taskType}Processor
 * 4. 必须处理所有可能的异常情况
 * 
 * @param <T> 业务参数类型
 * <AUTHOR>
 * @since 2025-07-31
 * @version 1.0 - 统一任务处理器架构
 */
public interface UniversalTaskProcessor<T> {

    /**
     * 处理任务的核心方法
     * 
     * 这是任务处理器的主要入口点，负责：
     * 1. 解析和验证业务参数
     * 2. 调用外部API或执行业务逻辑
     * 3. 处理同步和异步结果
     * 4. 返回统一的处理结果
     * 
     * 实现要求：
     * - 必须捕获所有异常并转换为TaskProcessResult
     * - 对于异步任务，应返回PROGRESS状态和外部任务ID
     * - 对于同步任务，应返回最终的SUCCESS或FAILED状态
     * - 必须设置适当的错误信息和重试建议
     * 
     * @param context 任务上下文，包含所有必要信息
     * @return 处理结果，不能为null
     * @throws IllegalArgumentException 当上下文参数无效时
     */
    TaskProcessResult process(TaskContext<T> context);

    /**
     * 处理异步回调
     * 
     * 用于处理外部API的异步回调通知，包括：
     * 1. 验证回调数据的有效性
     * 2. 更新任务状态和结果
     * 3. 处理回调中的错误信息
     * 4. 触发后续的业务流程
     * 
     * 实现要求：
     * - 必须验证taskId和callbackData的有效性
     * - 必须处理重复回调的情况
     * - 必须更新数据库中的任务状态
     * - 异常情况应记录日志但不抛出异常
     * 
     * @param taskId       任务ID，不能为null或空
     * @param callbackData 回调数据，不能为null
     * @throws IllegalArgumentException 当参数无效时
     */
    void handleCallback(String taskId, CallbackData callbackData);

    /**
     * 获取支持的任务类型
     * 
     * 用于任务处理器工厂的自动注册和路由，每个处理器
     * 必须返回唯一的任务类型标识。
     * 
     * @return 支持的任务类型，不能为null
     */
    TaskType getSupportedTaskType();

    /**
     * 检查处理器健康状态
     * 
     * 用于系统健康检查和故障转移，检查内容包括：
     * 1. 外部API连接状态
     * 2. 必要的配置参数
     * 3. 依赖服务的可用性
     * 4. 资源使用情况
     * 
     * 默认实现返回true，子类可以重写提供具体的健康检查逻辑。
     * 
     * @return 是否健康，true表示可以正常处理任务
     */
    default boolean isHealthy() {
        return true;
    }

    /**
     * 获取处理器版本信息
     * 
     * 用于版本管理和兼容性检查。
     * 
     * @return 处理器版本，默认为"1.0"
     */
    default String getVersion() {
        return "1.0";
    }

    /**
     * 获取处理器描述信息
     * 
     * 用于监控和管理界面显示。
     * 
     * @return 处理器描述
     */
    default String getDescription() {
        TaskType taskType = getSupportedTaskType();
        return taskType != null ? taskType.getDescription() + "处理器" : "未知任务处理器";
    }

    /**
     * 查询外部任务状态
     * 
     * 用于主动查询外部API的任务状态，适用于：
     * 1. 回调机制失效时的状态同步
     * 2. 定时状态检查
     * 3. 用户主动查询
     * 
     * 默认实现返回null，表示不支持状态查询。
     * 子类可以重写提供具体的状态查询逻辑。
     * 
     * @param externalJobId 外部任务ID
     * @return 回调数据，如果查询失败或不支持返回null
     */
    default CallbackData queryExternalStatus(String externalJobId) {
        return null;
    }

    /**
     * 取消外部任务
     * 
     * 用于取消正在执行的外部任务，适用于：
     * 1. 用户主动取消
     * 2. 系统超时取消
     * 3. 资源不足时的任务清理
     * 
     * 默认实现返回false，表示不支持取消操作。
     * 子类可以重写提供具体的取消逻辑。
     * 
     * @param externalJobId 外部任务ID
     * @return 是否成功取消
     */
    default boolean cancelExternalTask(String externalJobId) {
        return false;
    }

    /**
     * 获取处理器配置信息
     * 
     * 用于动态配置管理和监控展示。
     * 
     * @return 配置信息的键值对，默认为空
     */
    default java.util.Map<String, Object> getConfiguration() {
        return new java.util.HashMap<>();
    }

    /**
     * 获取处理器统计信息
     * 
     * 用于性能监控和分析，包括：
     * 1. 处理任务数量
     * 2. 成功/失败率
     * 3. 平均处理时间
     * 4. 错误统计
     * 
     * @return 统计信息的键值对，默认为空
     */
    default java.util.Map<String, Object> getStatistics() {
        return new java.util.HashMap<>();
    }

    /**
     * 预处理任务上下文
     * 
     * 在调用process方法之前执行，用于：
     * 1. 参数验证和预处理
     * 2. 权限检查
     * 3. 资源准备
     * 4. 上下文增强
     * 
     * 默认实现不做任何处理。
     * 子类可以重写提供具体的预处理逻辑。
     * 
     * @param context 任务上下文
     * @throws IllegalArgumentException 当上下文无效时
     * @throws IllegalStateException    当处理器状态不允许处理时
     */
    default void preProcess(TaskContext<T> context) {
        // 默认不做任何预处理
    }

    /**
     * 后处理任务结果
     * 
     * 在process方法执行完成后调用，用于：
     * 1. 结果验证和增强
     * 2. 日志记录
     * 3. 统计更新
     * 4. 通知发送
     * 
     * 默认实现不做任何处理。
     * 子类可以重写提供具体的后处理逻辑。
     * 
     * @param context 任务上下文
     * @param result  处理结果
     */
    default void postProcess(TaskContext<T> context, TaskProcessResult result) {
        // 默认不做任何后处理
    }

    /**
     * 处理器初始化
     * 
     * 在Spring容器启动时调用，用于：
     * 1. 初始化配置
     * 2. 建立连接
     * 3. 预热缓存
     * 4. 注册监听器
     * 
     * 默认实现不做任何初始化。
     * 子类可以重写提供具体的初始化逻辑。
     * 
     * @throws Exception 初始化失败时
     */
    default void initialize() throws Exception {
        // 默认不做任何初始化
    }

    /**
     * 处理器销毁
     * 
     * 在Spring容器关闭时调用，用于：
     * 1. 释放资源
     * 2. 关闭连接
     * 3. 清理缓存
     * 4. 保存状态
     * 
     * 默认实现不做任何清理。
     * 子类可以重写提供具体的清理逻辑。
     */
    default void destroy() {
        // 默认不做任何清理
    }
}
