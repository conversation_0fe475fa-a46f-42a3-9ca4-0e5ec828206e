package com.nacos.service.processor.impl;

import com.nacos.entity.context.TaskContext;
import com.nacos.entity.dto.AudioGenerationRequestDTO;
import com.nacos.entity.dto.CallbackData;
import com.nacos.entity.dto.TaskProcessResult;
import com.nacos.entity.enums.TaskType;
import com.nacos.entity.enums.UnifiedTaskStatusEnum;
import com.nacos.entity.po.DigitalAudioTaskPO;
import com.nacos.entity.vo.AudioGenerationResponseVO;
import com.nacos.mapper.DigitalAudioTaskMapper;
import com.nacos.result.Result;
import com.nacos.service.processor.AudioProviderProcessor;
import com.nacos.service.processor.AudioProviderProcessorFactory;
import com.nacos.service.processor.UniversalTaskProcessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 统一音频生成任务处理器
 * 
 * 实现UniversalTaskProcessor接口，处理音频生成任务的具体业务逻辑。
 * 主要功能包括：
 * 1. 参数解析和验证
 * 2. 多服务商API调用（MINIMAX、MICROSOFT等）
 * 3. 同步音频生成处理
 * 4. 任务状态管理
 * 5. 错误处理和重试
 * 
 * 设计原则：
 * 1. 统一接口：实现UniversalTaskProcessor统一接口
 * 2. 多服务商支持：通过AudioProviderProcessorFactory支持多种服务商
 * 3. 同步处理：音频生成通常为同步操作，直接返回结果
 * 4. 错误处理：完善的异常处理和错误恢复机制
 * 5. 监控统计：详细的处理统计和性能监控
 * 
 * <AUTHOR>
 * @since 2025-07-31
 * @version 1.0 - 统一任务处理器架构
 */
@Component("audioGenerateProcessor")
@Slf4j
public class UniversalAudioGenerateProcessor implements UniversalTaskProcessor<AudioGenerationRequestDTO> {

    @Autowired
    private DigitalAudioTaskMapper digitalAudioTaskMapper;

    @Autowired
    private AudioProviderProcessorFactory audioProviderProcessorFactory;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 处理器统计信息
     */
    private final Map<String, Object> statistics = new ConcurrentHashMap<>();

    /**
     * 服务启动时间
     */
    private final long startupTime = System.currentTimeMillis();

    @Override
    public TaskProcessResult process(TaskContext<AudioGenerationRequestDTO> context) {
        String methodName = "process";
        String taskId = context.getTaskId();
        String userId = context.getUserId();
        
        log.info("[{}] 开始处理音频生成任务: taskId={}, userId={}", methodName, taskId, userId);
        
        try {
            // 更新统计
            incrementStat("totalProcessed");
            
            // 1. 参数验证
            AudioGenerationRequestDTO requestDTO = context.getBusinessParams();
            if (!isValidRequest(requestDTO, userId)) {
                log.warn("[{}] 参数验证失败: taskId={}, userId={}", methodName, taskId, userId);
                incrementStat("validationFailures");
                return TaskProcessResult.failure("参数验证失败");
            }

            // 2. 查询任务记录
            DigitalAudioTaskPO taskPO = digitalAudioTaskMapper.selectById(taskId);
            if (taskPO == null) {
                log.error("[{}] 任务记录不存在: taskId={}", methodName, taskId);
                incrementStat("taskNotFound");
                return TaskProcessResult.failure("任务记录不存在");
            }

            // 3. 检查任务状态
            if (isFinalStatus(taskPO.getStatus())) {
                log.warn("[{}] 任务已处于最终状态: taskId={}, status={}", methodName, taskId, taskPO.getStatus());
                incrementStat("alreadyFinalStatus");
                return TaskProcessResult.success("任务已完成", null);
            }

            // 4. 更新任务状态为处理中
            updateTaskStatus(taskPO, UnifiedTaskStatusEnum.PROGRESS.getCode(), null);

            // 5. 获取服务商处理器
            String provider = requestDTO.getProvider();
            Optional<AudioProviderProcessor> processorOpt = audioProviderProcessorFactory.getProcessor(provider);
            if (!processorOpt.isPresent()) {
                log.error("[{}] 不支持的服务商: taskId={}, provider={}", methodName, taskId, provider);
                incrementStat("unsupportedProvider");
                updateTaskStatus(taskPO, UnifiedTaskStatusEnum.FAILED.getCode(), "不支持的服务商: " + provider);
                return TaskProcessResult.failure("不支持的服务商: " + provider);
            }

            AudioProviderProcessor processor = processorOpt.get();

            // 6. 调用服务商API进行音频生成
            Result<AudioGenerationResponseVO> apiResult = processor.processRequest(requestDTO, userId);
            
            if (!apiResult.isSuccess() || apiResult.getData() == null) {
                log.error("[{}] 音频生成API调用失败: taskId={}, provider={}, error={}", 
                         methodName, taskId, provider, apiResult.getMessage());
                incrementStat("apiCallFailures");
                updateTaskStatus(taskPO, UnifiedTaskStatusEnum.FAILED.getCode(), apiResult.getMessage());
                return TaskProcessResult.retryableFailure("音频生成失败：" + apiResult.getMessage(), 300);
            }

            // 7. 处理成功结果
            AudioGenerationResponseVO responseVO = apiResult.getData();
            String audioUrl = responseVO.getAudioUrl();
            Integer durationMs = responseVO.getDurationMs();

            if (!StringUtils.hasText(audioUrl)) {
                log.error("[{}] 音频生成返回的URL为空: taskId={}, provider={}", methodName, taskId, provider);
                incrementStat("invalidApiResponse");
                updateTaskStatus(taskPO, UnifiedTaskStatusEnum.FAILED.getCode(), "音频生成返回的URL为空");
                return TaskProcessResult.failure("音频生成返回的URL为空");
            }

            // 8. 更新任务状态为成功
            updateTaskStatusToSuccess(taskPO, audioUrl, durationMs, responseVO);

            // 9. 构建处理结果
            Map<String, Object> resultData = new HashMap<>();
            resultData.put("audioUrl", audioUrl);
            resultData.put("durationMs", durationMs);
            resultData.put("provider", provider);
            resultData.put("fileSize", responseVO.getFileSize());
            resultData.put("audioFormat", responseVO.getAudioFormat());
            resultData.put("completedTime", System.currentTimeMillis());

            incrementStat("successfulGenerations");
            log.info("[{}] 音频生成任务成功完成: taskId={}, provider={}, audioUrl={}, duration={}ms",
                    methodName, taskId, provider, audioUrl, durationMs);

            TaskProcessResult result = TaskProcessResult.success("音频生成成功");
            result.setResultData(resultData);
            return result;

        } catch (Exception e) {
            log.error("[{}] 处理音频生成任务异常: taskId={}, userId={}", methodName, taskId, userId, e);
            incrementStat("processingErrors");
            
            // 更新任务状态为失败
            try {
                DigitalAudioTaskPO taskPO = digitalAudioTaskMapper.selectById(taskId);
                if (taskPO != null) {
                    updateTaskStatus(taskPO, UnifiedTaskStatusEnum.FAILED.getCode(), "处理异常：" + e.getMessage());
                }
            } catch (Exception updateEx) {
                log.error("[{}] 更新任务状态失败", methodName, updateEx);
            }
            
            return TaskProcessResult.retryableFailure("处理任务异常：" + e.getMessage(), 300);
        }
    }

    @Override
    public void handleCallback(String taskId, CallbackData callbackData) {
        String methodName = "handleCallback";
        log.info("[{}] 音频生成任务不支持异步回调: taskId={}", methodName, taskId);
        
        // 音频生成通常为同步操作，不需要异步回调处理
        // 如果将来需要支持异步音频生成，可以在这里实现回调逻辑
        incrementStat("callbacksIgnored");
    }

    @Override
    public TaskType getSupportedTaskType() {
        return TaskType.AUDIO_GENERATE;
    }

    @Override
    public boolean isHealthy() {
        try {
            // 检查音频处理器工厂是否正常
            return audioProviderProcessorFactory != null &&
                   audioProviderProcessorFactory.getSupportedProviders().size() > 0;
        } catch (Exception e) {
            log.error("健康检查失败", e);
            return false;
        }
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    @Override
    public String getDescription() {
        return "统一音频生成任务处理器 - 支持多服务商（MINIMAX、MICROSOFT等）";
    }

    @Override
    public CallbackData queryExternalStatus(String externalJobId) {
        String methodName = "queryExternalStatus";
        log.debug("[{}] 音频生成任务不支持外部状态查询: externalJobId={}", methodName, externalJobId);
        
        // 音频生成通常为同步操作，不需要外部状态查询
        return null;
    }

    @Override
    public boolean cancelExternalTask(String externalJobId) {
        String methodName = "cancelExternalTask";
        log.info("[{}] 音频生成任务不支持外部任务取消: externalJobId={}", methodName, externalJobId);
        
        // 音频生成通常为同步操作，不支持任务取消
        return false;
    }

    @Override
    public Map<String, Object> getConfiguration() {
        Map<String, Object> config = new HashMap<>();
        config.put("supportedTaskType", getSupportedTaskType().getCode());
        config.put("description", getDescription());
        config.put("version", getVersion());
        config.put("healthy", isHealthy());
        config.put("startupTime", startupTime);
        
        // 添加支持的服务商信息
        try {
            config.put("supportedProviders", audioProviderProcessorFactory.getSupportedProviders());
        } catch (Exception e) {
            log.warn("获取支持的服务商信息失败", e);
            config.put("supportedProviders", "unknown");
        }
        
        return config;
    }

    @Override
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>(statistics);
        stats.put("startupTime", startupTime);
        stats.put("uptime", System.currentTimeMillis() - startupTime);
        
        // 计算成功率
        Long totalProcessed = (Long) statistics.getOrDefault("totalProcessed", 0L);
        Long successfulGenerations = (Long) statistics.getOrDefault("successfulGenerations", 0L);
        if (totalProcessed > 0) {
            double successRate = (double) successfulGenerations / totalProcessed * 100;
            stats.put("successRate", String.format("%.2f%%", successRate));
        } else {
            stats.put("successRate", "N/A");
        }
        
        return stats;
    }

    // ==================== 私有方法 ====================

    /**
     * 验证请求参数
     */
    private boolean isValidRequest(AudioGenerationRequestDTO requestDTO, String userId) {
        if (requestDTO == null) {
            return false;
        }
        
        if (!StringUtils.hasText(userId)) {
            return false;
        }
        
        if (!StringUtils.hasText(requestDTO.getProvider())) {
            return false;
        }
        
        if (requestDTO.getTtsParams() == null) {
            return false;
        }
        
        if (!StringUtils.hasText(requestDTO.getTtsParams().getText())) {
            return false;
        }
        
        if (!StringUtils.hasText(requestDTO.getTtsParams().getVoiceId())) {
            return false;
        }
        
        return true;
    }

    /**
     * 检查是否为最终状态
     */
    private boolean isFinalStatus(Integer status) {
        if (status == null) {
            return false;
        }
        // 状态：0-排队中 1-进行中 2-生成成功 3-失败 4-超时 5-已取消
        return status == 2 || status == 3 || status == 4 || status == 5;
    }

    /**
     * 更新任务状态
     */
    private void updateTaskStatus(DigitalAudioTaskPO taskPO, Integer status, String errorMessage) {
        try {
            taskPO.setStatus(status);
            if (StringUtils.hasText(errorMessage)) {
                taskPO.setErrorMsg(errorMessage);
            }
            taskPO.setUpdateTime(LocalDateTime.now());
            
            digitalAudioTaskMapper.updateById(taskPO);
            
        } catch (Exception e) {
            log.error("更新任务状态失败: taskId={}, status={}", taskPO.getTaskId(), status, e);
        }
    }

    /**
     * 更新任务状态为成功
     */
    private void updateTaskStatusToSuccess(DigitalAudioTaskPO taskPO, String audioUrl, Integer durationMs, 
                                         AudioGenerationResponseVO responseVO) {
        try {
            taskPO.setStatus(UnifiedTaskStatusEnum.SUCCESS.getCode());
            taskPO.setGeneratedAudioUrl(audioUrl);
            taskPO.setDurationMs(durationMs);
            taskPO.setUpdateTime(LocalDateTime.now());
            
            // 存储详细的响应信息到JSON字段
            if (responseVO.getSubtitles() != null && !responseVO.getSubtitles().isEmpty()) {
                try {
                    taskPO.setSubtitlesJson(objectMapper.writeValueAsString(responseVO.getSubtitles()));
                } catch (Exception e) {
                    log.warn("序列化字幕信息失败", e);
                }
            }
            
            digitalAudioTaskMapper.updateById(taskPO);
            
            log.info("音频生成任务状态更新为成功: taskId={}, audioUrl={}, duration={}ms", 
                    taskPO.getTaskId(), audioUrl, durationMs);
            
        } catch (Exception e) {
            log.error("更新任务状态为成功失败: taskId={}", taskPO.getTaskId(), e);
        }
    }

    /**
     * 增加统计计数
     */
    private void incrementStat(String key) {
        statistics.merge(key, 1L, (oldValue, newValue) -> 
                ((Number) oldValue).longValue() + ((Number) newValue).longValue());
    }
}
