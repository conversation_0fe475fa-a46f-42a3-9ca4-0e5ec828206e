package com.nacos.service.processor.impl;

import com.nacos.entity.context.TaskContext;
import com.nacos.entity.dto.CallbackData;
import com.nacos.entity.dto.TaskProcessResult;
import com.nacos.entity.dto.VideoTranslateRequestDTO;
import com.nacos.entity.enums.TaskType;
import com.nacos.entity.enums.UnifiedTaskStatusEnum;
import com.nacos.entity.po.VideoTranslateTaskPO;
import com.nacos.mapper.VideoTranslateTaskMapper;
import com.nacos.result.Result;
import com.nacos.service.processor.UniversalTaskProcessor;
import com.nacos.service.processor.VideoTranslateProcessor;
import com.nacos.service.processor.VideoTranslateProcessorFactory;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 统一视频翻译任务处理器
 *
 * 实现UniversalTaskProcessor接口，处理视频翻译任务的具体业务逻辑。
 * 主要功能包括：
 * 1. 参数解析和验证
 * 2. 多服务商处理器调用
 * 3. 异步回调处理
 * 4. 任务状态管理
 * 5. 错误处理和重试
 * 6. 服务商故障转移
 *
 * 设计原则：
 * 1. 统一接口：实现UniversalTaskProcessor统一接口
 * 2. 异步处理：支持异步任务提交和回调处理
 * 3. 状态管理：完整的任务状态生命周期管理
 * 4. 错误处理：完善的异常处理和错误恢复机制
 * 5. 监控统计：详细的处理统计和性能监控
 * 6. 多服务商支持：通过工厂模式支持多个视频翻译服务商
 *
 * <AUTHOR>
 * @since 2025-07-31
 * @version 2.0 - 多服务商架构支持
 */
@Component("videoTranslateProcessor")
@Slf4j
public class UniversalVideoTranslateProcessor implements UniversalTaskProcessor<VideoTranslateRequestDTO> {

    @Autowired
    private VideoTranslateTaskMapper videoTranslateTaskMapper;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private VideoTranslateProcessorFactory processorFactory;

    /**
     * 处理器统计信息
     */
    private final Map<String, Object> statistics = new ConcurrentHashMap<>();

    /**
     * 服务启动时间
     */
    private final long startupTime = System.currentTimeMillis();

    @Override
    public TaskProcessResult process(TaskContext<VideoTranslateRequestDTO> context) {
        String methodName = "process";
        String taskId = context.getTaskId();
        String userId = context.getUserId();
        
        log.info("[{}] 开始处理视频翻译任务: taskId={}, userId={}", methodName, taskId, userId);
        
        try {
            // 更新统计
            incrementStat("totalProcessed");
            
            // 1. 参数验证
            VideoTranslateRequestDTO requestDTO = context.getBusinessParams();
            if (!isValidRequest(requestDTO, userId)) {
                log.warn("[{}] 参数验证失败: taskId={}, userId={}", methodName, taskId, userId);
                incrementStat("validationFailures");
                return TaskProcessResult.failure("参数验证失败");
            }

            // 2. 查询任务记录
            VideoTranslateTaskPO taskPO = videoTranslateTaskMapper.getTaskByTaskId(taskId);
            if (taskPO == null) {
                log.error("[{}] 任务记录不存在: taskId={}", methodName, taskId);
                incrementStat("taskNotFound");
                return TaskProcessResult.failure("任务记录不存在");
            }

            // 3. 检查任务状态
            if (taskPO.isFinalStatus()) {
                log.warn("[{}] 任务已处于最终状态: taskId={}, status={}", methodName, taskId, taskPO.getStatus());
                incrementStat("alreadyFinalStatus");
                return TaskProcessResult.success("任务已完成", null);
            }

            // 4. 选择服务商处理器
            String selectedProvider = selectProvider(taskPO, requestDTO);
            Optional<VideoTranslateProcessor> processorOpt = processorFactory.getProcessor(selectedProvider);

            if (!processorOpt.isPresent()) {
                log.error("[{}] 无法获取服务商处理器: taskId={}, provider={}", methodName, taskId, selectedProvider);
                incrementStat("processorNotFound");
                return TaskProcessResult.retryableFailure("无法获取服务商处理器：" + selectedProvider, 300);
            }

            VideoTranslateProcessor processor = processorOpt.get();
            log.info("[{}] 使用服务商处理器: taskId={}, provider={}", methodName, taskId, processor.getProviderName());

            // 5. 调用服务商处理器提交任务
            Result<VideoTranslateProcessor.VideoTranslateResult> processorResult = processor.processRequest(requestDTO, userId);

            if (!processorResult.isSuccess() || processorResult.getData() == null) {
                log.error("[{}] 服务商处理器调用失败: taskId={}, provider={}, error={}",
                         methodName, taskId, processor.getProviderName(), processorResult.getMessage());
                incrementStat("processorCallFailures");

                // 尝试故障转移
                return handleProcessorFailure(taskPO, requestDTO, userId, selectedProvider, processorResult.getMessage());
            }

            // 6. 解析处理器响应
            VideoTranslateProcessor.VideoTranslateResult result = processorResult.getData();
            String providerTaskId = result.getProviderTaskId();

            if (!StringUtils.hasText(providerTaskId)) {
                log.error("[{}] 服务商返回的任务ID为空: taskId={}, provider={}", methodName, taskId, processor.getProviderName());
                incrementStat("invalidProcessorResponse");
                return TaskProcessResult.retryableFailure("服务商返回的任务ID为空", 300);
            }

            // 7. 更新任务状态和服务商信息
            updateTaskWithProvider(taskPO, UnifiedTaskStatusEnum.PROGRESS.getCode(), providerTaskId, processor.getProviderName(), null);

            // 8. 构建处理结果
            Map<String, Object> resultData = new HashMap<>();
            resultData.put("providerTaskId", providerTaskId);
            resultData.put("provider", processor.getProviderName());
            resultData.put("status", result.getStatus());
            resultData.put("progress", result.getProgress());
            resultData.put("submitTime", System.currentTimeMillis());

            incrementStat("successfulSubmissions");
            log.info("[{}] 视频翻译任务提交成功: taskId={}, provider={}, providerTaskId={}",
                    methodName, taskId, processor.getProviderName(), providerTaskId);

            return TaskProcessResult.processing(providerTaskId);

        } catch (Exception e) {
            log.error("[{}] 处理视频翻译任务异常: taskId={}, userId={}", methodName, taskId, userId, e);
            incrementStat("processingErrors");
            return TaskProcessResult.retryableFailure("处理任务异常：" + e.getMessage(), 300);
        }
    }

    @Override
    public void handleCallback(String taskId, CallbackData callbackData) {
        String methodName = "handleCallback";
        log.info("[{}] 处理视频翻译回调: taskId={}, externalJobId={}, success={}", 
                methodName, taskId, callbackData.getExternalJobId(), callbackData.getSuccess());
        
        try {
            // 更新统计
            incrementStat("totalCallbacks");
            
            // 1. 查询任务记录
            VideoTranslateTaskPO taskPO = videoTranslateTaskMapper.getTaskByTaskId(taskId);
            if (taskPO == null) {
                log.error("[{}] 回调处理失败，任务记录不存在: taskId={}", methodName, taskId);
                incrementStat("callbackTaskNotFound");
                return;
            }

            // 2. 检查任务状态
            if (taskPO.isFinalStatus()) {
                log.warn("[{}] 任务已处于最终状态，忽略回调: taskId={}, status={}", methodName, taskId, taskPO.getStatus());
                incrementStat("callbackIgnored");
                return;
            }

            // 3. 根据回调结果更新任务状态
            if (callbackData.getSuccess()) {
                // 成功回调
                handleSuccessCallback(taskPO, callbackData);
                incrementStat("successfulCallbacks");
            } else {
                // 失败回调
                handleFailureCallback(taskPO, callbackData);
                incrementStat("failedCallbacks");
            }

            log.info("[{}] 视频翻译回调处理完成: taskId={}, externalJobId={}", 
                    methodName, taskId, callbackData.getExternalJobId());

        } catch (Exception e) {
            log.error("[{}] 处理视频翻译回调异常: taskId={}, externalJobId={}", 
                    methodName, taskId, callbackData.getExternalJobId(), e);
            incrementStat("callbackErrors");
        }
    }

    @Override
    public TaskType getSupportedTaskType() {
        return TaskType.VIDEO_TRANSLATE;
    }

    @Override
    public boolean isHealthy() {
        try {
            // 检查是否有健康的服务商处理器
            List<VideoTranslateProcessor> healthyProcessors = processorFactory.getHealthyProcessors();
            return !healthyProcessors.isEmpty();
        } catch (Exception e) {
            log.error("健康检查失败", e);
            return false;
        }
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    @Override
    public String getDescription() {
        return "统一视频翻译任务处理器 - 支持多服务商架构";
    }

    @Override
    public CallbackData queryExternalStatus(String externalJobId) {
        String methodName = "queryExternalStatus";
        try {
            log.debug("[{}] 查询外部任务状态: externalJobId={}", methodName, externalJobId);
            
            // TODO: 需要根据任务记录中的服务商信息来选择对应的处理器进行状态查询
            // 当前暂时返回null，实际实现需要：
            // 1. 根据externalJobId查询任务记录获取服务商信息
            // 2. 获取对应的服务商处理器
            // 3. 调用处理器的checkTaskStatus方法

            log.warn("[{}] 多服务商状态查询功能待实现: externalJobId={}", methodName, externalJobId);
            return null;

        } catch (Exception e) {
            log.error("[{}] 查询外部任务状态异常: externalJobId={}", methodName, externalJobId, e);
            return null;
        }
    }

    @Override
    public boolean cancelExternalTask(String externalJobId) {
        String methodName = "cancelExternalTask";
        try {
            log.info("[{}] 取消外部任务: externalJobId={}", methodName, externalJobId);
            
            // 羚羊平台暂不支持任务取消，返回false
            log.warn("[{}] 羚羊平台暂不支持任务取消: externalJobId={}", methodName, externalJobId);
            return false;

        } catch (Exception e) {
            log.error("[{}] 取消外部任务异常: externalJobId={}", methodName, externalJobId, e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getConfiguration() {
        Map<String, Object> config = new HashMap<>();
        config.put("supportedTaskType", getSupportedTaskType().getCode());
        config.put("description", getDescription());
        config.put("version", getVersion());
        config.put("healthy", isHealthy());
        config.put("availableProviders", processorFactory.getHealthyProcessors().size());
        config.put("startupTime", startupTime);
        return config;
    }

    @Override
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>(statistics);
        stats.put("startupTime", startupTime);
        stats.put("uptime", System.currentTimeMillis() - startupTime);
        
        // 计算成功率
        Long totalProcessed = (Long) statistics.getOrDefault("totalProcessed", 0L);
        Long successfulSubmissions = (Long) statistics.getOrDefault("successfulSubmissions", 0L);
        if (totalProcessed > 0) {
            double successRate = (double) successfulSubmissions / totalProcessed * 100;
            stats.put("successRate", String.format("%.2f%%", successRate));
        } else {
            stats.put("successRate", "N/A");
        }
        
        return stats;
    }

    // ==================== 私有方法 ====================

    /**
     * 选择服务商处理器
     *
     * @param taskPO 任务记录
     * @param requestDTO 请求参数
     * @return 选择的服务商名称
     */
    private String selectProvider(VideoTranslateTaskPO taskPO, VideoTranslateRequestDTO requestDTO) {
        String methodName = "selectProvider";

        // 1. 检查任务记录中是否已指定服务商
        if (StringUtils.hasText(taskPO.getProvider())) {
            log.debug("[{}] 使用任务记录中指定的服务商: {}", methodName, taskPO.getProvider());
            return taskPO.getProvider();
        }

        // 2. 检查扩展参数中是否指定服务商
        if (StringUtils.hasText(requestDTO.getExtParams())) {
            try {
                @SuppressWarnings("unchecked")
                Map<String, Object> extParams = objectMapper.readValue(requestDTO.getExtParams(), Map.class);
                String extProvider = (String) extParams.get("provider");
                if (StringUtils.hasText(extProvider)) {
                    log.debug("[{}] 使用扩展参数中指定的服务商: {}", methodName, extProvider);
                    return extProvider;
                }
            } catch (Exception e) {
                log.debug("[{}] 解析扩展参数失败，忽略: {}", methodName, e.getMessage());
            }
        }

        // 3. 根据业务规则选择服务商（可扩展）
        String selectedProvider = selectProviderByBusinessRules(requestDTO);
        if (StringUtils.hasText(selectedProvider)) {
            log.debug("[{}] 根据业务规则选择服务商: {}", methodName, selectedProvider);
            return selectedProvider;
        }

        // 4. 使用默认的健康服务商（按优先级排序）
        List<VideoTranslateProcessor> healthyProcessors = processorFactory.getHealthyProcessors();
        if (!healthyProcessors.isEmpty()) {
            String defaultProvider = healthyProcessors.get(0).getProviderName();
            log.debug("[{}] 使用默认健康服务商: {}", methodName, defaultProvider);
            return defaultProvider;
        }

        // 5. 兜底：返回羚羊服务商
        log.warn("[{}] 无健康服务商可用，使用兜底服务商: LINGYANG", methodName);
        return "LINGYANG";
    }

    /**
     * 根据业务规则选择服务商
     *
     * @param requestDTO 请求参数
     * @return 选择的服务商名称，如果无特殊规则则返回null
     */
    private String selectProviderByBusinessRules(VideoTranslateRequestDTO requestDTO) {
        // TODO: 可根据具体业务需求实现选择逻辑
        // 例如：根据语言对、视频时长、用户等级等选择不同服务商

        // 示例：特定语言对使用特定服务商
        // if ("zh".equals(requestDTO.getSourceLanguage()) && "en".equals(requestDTO.getTargetLanguage())) {
        //     return "ALIYUN";
        // }

        return null; // 暂无特殊规则
    }

    /**
     * 处理服务商处理器调用失败，尝试故障转移
     *
     * @param taskPO 任务记录
     * @param requestDTO 请求参数
     * @param userId 用户ID
     * @param failedProvider 失败的服务商
     * @param errorMessage 错误信息
     * @return 处理结果
     */
    private TaskProcessResult handleProcessorFailure(VideoTranslateTaskPO taskPO, VideoTranslateRequestDTO requestDTO,
                                                   String userId, String failedProvider, String errorMessage) {
        String methodName = "handleProcessorFailure";

        log.warn("[{}] 服务商{}处理失败，尝试故障转移: taskId={}, error={}",
                methodName, failedProvider, taskPO.getTaskId(), errorMessage);

        // 获取故障转移处理器列表
        List<VideoTranslateProcessor> fallbackProcessors = processorFactory.getFallbackProcessors(failedProvider);

        if (fallbackProcessors.isEmpty()) {
            log.error("[{}] 无可用的故障转移处理器: taskId={}, failedProvider={}",
                     methodName, taskPO.getTaskId(), failedProvider);
            incrementStat("noFallbackProcessors");
            return TaskProcessResult.retryableFailure("服务商处理失败且无可用备用服务商：" + errorMessage, 600);
        }

        // 尝试第一个可用的故障转移处理器
        VideoTranslateProcessor fallbackProcessor = fallbackProcessors.get(0);
        log.info("[{}] 使用故障转移处理器: taskId={}, fallbackProvider={}",
                methodName, taskPO.getTaskId(), fallbackProcessor.getProviderName());

        try {
            Result<VideoTranslateProcessor.VideoTranslateResult> fallbackResult =
                fallbackProcessor.processRequest(requestDTO, userId);

            if (fallbackResult.isSuccess() && fallbackResult.getData() != null) {
                VideoTranslateProcessor.VideoTranslateResult result = fallbackResult.getData();
                String providerTaskId = result.getProviderTaskId();

                if (StringUtils.hasText(providerTaskId)) {
                    // 更新任务状态和服务商信息
                    updateTaskWithProvider(taskPO, UnifiedTaskStatusEnum.PROGRESS.getCode(),
                                         providerTaskId, fallbackProcessor.getProviderName(),
                                         "故障转移自" + failedProvider);

                    incrementStat("successfulFallbacks");
                    log.info("[{}] 故障转移成功: taskId={}, fallbackProvider={}, providerTaskId={}",
                            methodName, taskPO.getTaskId(), fallbackProcessor.getProviderName(), providerTaskId);

                    return TaskProcessResult.processing(providerTaskId);
                }
            }

            log.error("[{}] 故障转移处理器也失败: taskId={}, fallbackProvider={}, error={}",
                     methodName, taskPO.getTaskId(), fallbackProcessor.getProviderName(), fallbackResult.getMessage());

        } catch (Exception e) {
            log.error("[{}] 故障转移处理器异常: taskId={}, fallbackProvider={}",
                     methodName, taskPO.getTaskId(), fallbackProcessor.getProviderName(), e);
        }

        incrementStat("failedFallbacks");
        return TaskProcessResult.retryableFailure("主服务商和故障转移服务商均失败：" + errorMessage, 600);
    }

    /**
     * 验证请求参数
     */
    private boolean isValidRequest(VideoTranslateRequestDTO requestDTO, String userId) {
        if (requestDTO == null) {
            return false;
        }
        
        if (!StringUtils.hasText(userId)) {
            return false;
        }
        
        if (!StringUtils.hasText(requestDTO.getVideoUrl())) {
            return false;
        }
        
        if (!StringUtils.hasText(requestDTO.getSourceLanguage())) {
            return false;
        }
        
        if (!StringUtils.hasText(requestDTO.getTargetLanguage())) {
            return false;
        }
        
        if (!StringUtils.hasText(requestDTO.getVoiceId())) {
            return false;
        }
        
        // 检查源语言和目标语言是否相同
        if (requestDTO.getSourceLanguage().equals(requestDTO.getTargetLanguage())) {
            return false;
        }
        
        return true;
    }



    /**
     * 更新任务状态和服务商信息
     */
    private void updateTaskWithProvider(VideoTranslateTaskPO taskPO, Integer status, String providerTaskId,
                                      String provider, String remark) {
        try {
            taskPO.setStatus(status);
            taskPO.setProvider(provider);

            // 构建结果数据
            Map<String, Object> resultData = new HashMap<>();
            resultData.put("providerTaskId", providerTaskId);
            resultData.put("provider", provider);
            resultData.put("updateTime", System.currentTimeMillis());

            if (StringUtils.hasText(remark)) {
                resultData.put("remark", remark);
            }

            try {
                taskPO.setResultJson(objectMapper.writeValueAsString(resultData));
            } catch (Exception e) {
                log.warn("序列化结果数据失败", e);
            }

            taskPO.setUpdateTime(LocalDateTime.now());
            videoTranslateTaskMapper.updateById(taskPO);

            log.debug("任务状态和服务商信息更新成功: taskId={}, status={}, provider={}, providerTaskId={}",
                     taskPO.getTaskId(), status, provider, providerTaskId);

        } catch (Exception e) {
            log.error("更新任务状态和服务商信息失败: taskId={}", taskPO.getTaskId(), e);
        }
    }



    /**
     * 处理成功回调
     */
    private void handleSuccessCallback(VideoTranslateTaskPO taskPO, CallbackData callbackData) {
        // 更新任务状态为成功
        taskPO.setStatus(UnifiedTaskStatusEnum.SUCCESS.getCode());
        taskPO.setTranslatedVideoUrl(callbackData.getResultUrl());
        // VideoTranslateTaskPO没有progress字段，使用resultJson存储进度信息
        try {
            Map<String, Object> resultData = new HashMap<>();
            resultData.put("progress", 100);
            resultData.put("resultUrl", callbackData.getResultUrl());
            resultData.put("completedTime", System.currentTimeMillis());
            taskPO.setResultJson(objectMapper.writeValueAsString(resultData));
        } catch (Exception e) {
            log.warn("序列化成功结果数据失败", e);
        }
        taskPO.setUpdateTime(LocalDateTime.now());

        videoTranslateTaskMapper.updateById(taskPO);

        log.info("视频翻译任务成功完成: taskId={}, resultUrl={}",
                taskPO.getTaskId(), callbackData.getResultUrl());
    }

    /**
     * 处理失败回调
     */
    private void handleFailureCallback(VideoTranslateTaskPO taskPO, CallbackData callbackData) {
        // 更新任务状态为失败
        taskPO.setStatus(UnifiedTaskStatusEnum.FAILED.getCode());
        taskPO.setErrorMsg(callbackData.getErrorMessage());
        taskPO.setUpdateTime(LocalDateTime.now());

        videoTranslateTaskMapper.updateById(taskPO);

        log.warn("视频翻译任务失败: taskId={}, error={}",
                taskPO.getTaskId(), callbackData.getErrorMessage());
    }



    /**
     * 增加统计计数
     */
    private void incrementStat(String key) {
        statistics.merge(key, 1L, (oldValue, newValue) -> 
                ((Number) oldValue).longValue() + ((Number) newValue).longValue());
    }
}
