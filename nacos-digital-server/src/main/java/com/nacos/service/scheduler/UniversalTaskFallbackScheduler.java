package com.nacos.service.scheduler;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nacos.entity.dto.TaskMessage;
import com.nacos.entity.enums.*;
import com.nacos.entity.po.*;
import com.nacos.mapper.*;
import com.nacos.service.message.UniversalTaskMessageSender;
import com.nacos.service.message.UniversalTaskQueueHealthChecker;
import com.nacos.service.migrator.TaskStatusMigrator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 统一任务兜底定时调度器
 * 
 * 作为Redis消息队列故障时的兜底机制，确保任务不丢失。
 * 
 * 主要功能：
 * 1. 定时扫描未处理任务，重新发送到消息队列
 * 2. 检查Redis队列健康状态，故障时启用兜底机制
 * 3. 处理超时任务，自动标记为超时状态
 * 4. 监控任务处理性能，提供统计信息
 * 5. 支持任务重试和故障恢复
 * 
 * 设计原则：
 * 1. 兜底保障：确保任务不因Redis故障而丢失
 * 2. 智能检测：只在必要时启用兜底机制，避免重复处理
 * 3. 性能优化：批量处理任务，减少数据库压力
 * 4. 可配置性：支持通过配置文件调整扫描频率和超时时间
 * 5. 监控友好：提供详细的统计信息和健康状态
 * 
 * 兜底策略：
 * 1. Redis正常时：定期检查，不执行兜底操作
 * 2. Redis故障时：启用兜底扫描，重新发送任务到队列
 * 3. 任务超时时：自动标记超时，避免无限等待
 * 4. 系统恢复时：自动恢复正常模式
 * 
 * <AUTHOR>
 * @since 2025-07-31
 * @version 1.0 - 统一任务兜底调度架构
 */
@Component
@Slf4j
@ConditionalOnProperty(name = "universal.task.fallback.enabled", havingValue = "true", matchIfMissing = true)
public class UniversalTaskFallbackScheduler {

    @Autowired
    private UniversalTaskQueueHealthChecker queueHealthChecker;

    @Autowired
    private UniversalTaskMessageSender messageSender;

    @Autowired
    private TaskStatusMigrator statusMigrator;

    // 数据库映射器
    @Autowired
    private VideoTranslateTaskMapper videoTranslateTaskMapper;

    @Autowired
    private DigitalAudioTaskMapper digitalAudioTaskMapper;

    @Autowired
    private VideoEditTaskMapper videoEditTaskMapper;

    /**
     * 兜底调度统计信息
     */
    private final Map<String, Object> fallbackStats = new ConcurrentHashMap<>();

    /**
     * 服务启动时间
     */
    private final long startupTime = System.currentTimeMillis();

    /**
     * 上次Redis健康检查结果
     */
    private volatile boolean lastRedisHealthy = true;

    /**
     * 兜底模式状态
     */
    private volatile boolean fallbackModeActive = false;

    // ==================== 主要定时任务 ====================

    /**
     * 定时检查Redis队列健康状态
     * 执行频率：每30秒检查一次
     */
    @Scheduled(fixedRateString = "${universal.task.fallback.health-check-rate:30000}")
    public void checkQueueHealthStatus() {
        try {
            log.debug("Checking queue health status for fallback decision");

            // 检查所有队列健康状态
            Map<String, Object> healthReport = queueHealthChecker.checkAllQueuesHealth();
            boolean currentlyHealthy = (Boolean) healthReport.getOrDefault("overallHealthy", false);

            // 更新健康状态
            boolean healthStatusChanged = (lastRedisHealthy != currentlyHealthy);
            lastRedisHealthy = currentlyHealthy;

            // 根据健康状态决定是否启用兜底模式
            if (!currentlyHealthy && !fallbackModeActive) {
                activateFallbackMode();
            } else if (currentlyHealthy && fallbackModeActive) {
                deactivateFallbackMode();
            }

            // 记录健康状态变化
            if (healthStatusChanged) {
                log.info("Queue health status changed: healthy={}, fallbackMode={}", 
                        currentlyHealthy, fallbackModeActive);
                incrementStat("healthStatusChanges");
            }

            incrementStat("healthChecks");

        } catch (Exception e) {
            log.error("Failed to check queue health status", e);
            incrementStat("healthCheckErrors");
            
            // 健康检查失败时，保守地启用兜底模式
            if (!fallbackModeActive) {
                log.warn("Health check failed, activating fallback mode as precaution");
                activateFallbackMode();
            }
        }
    }

    /**
     * 兜底任务扫描 - 仅在Redis故障时执行
     * 执行频率：每2分钟扫描一次（仅在兜底模式下）
     */
    @Scheduled(fixedRateString = "${universal.task.fallback.scan-rate:120000}")
    public void fallbackTaskScan() {
        // 只在兜底模式下执行
        if (!fallbackModeActive) {
            return;
        }

        try {
            log.info("Starting fallback task scan (Redis unhealthy mode)");
            long startTime = System.currentTimeMillis();

            // 扫描各种类型的未处理任务
            int totalRescued = 0;
            totalRescued += scanAndRescueVideoTranslateTasks();
            totalRescued += scanAndRescueAudioGenerationTasks();
            totalRescued += scanAndRescueVideoEditTasks();

            long scanTime = System.currentTimeMillis() - startTime;
            log.info("Fallback task scan completed: rescued={}, time={}ms", totalRescued, scanTime);

            // 更新统计
            incrementStat("fallbackScans");
            addToStat("totalTasksRescued", totalRescued);
            addToStat("totalScanTime", scanTime);

        } catch (Exception e) {
            log.error("Fallback task scan failed", e);
            incrementStat("fallbackScanErrors");
        }
    }

    /**
     * 超时任务处理 - 无论Redis状态如何都执行
     * 执行频率：每5分钟检查一次
     */
    @Scheduled(fixedRateString = "${universal.task.fallback.timeout-check-rate:300000}")
    public void processTimeoutTasks() {
        try {
            log.debug("Starting timeout task processing");
            long startTime = System.currentTimeMillis();

            // 处理各种类型的超时任务
            int totalTimeout = 0;
            totalTimeout += processVideoTranslateTimeoutTasks();
            totalTimeout += processAudioGenerationTimeoutTasks();
            totalTimeout += processVideoEditTimeoutTasks();

            long processTime = System.currentTimeMillis() - startTime;
            log.debug("Timeout task processing completed: timeout={}, time={}ms", totalTimeout, processTime);

            // 更新统计
            incrementStat("timeoutChecks");
            addToStat("totalTimeoutTasks", totalTimeout);

        } catch (Exception e) {
            log.error("Timeout task processing failed", e);
            incrementStat("timeoutCheckErrors");
        }
    }

    // ==================== 兜底模式管理 ====================

    /**
     * 激活兜底模式
     */
    private void activateFallbackMode() {
        if (!fallbackModeActive) {
            fallbackModeActive = true;
            log.warn("Fallback mode ACTIVATED - Redis queue unhealthy, starting task rescue operations");
            incrementStat("fallbackActivations");
            setStat("fallbackActivatedAt", System.currentTimeMillis());
        }
    }

    /**
     * 停用兜底模式
     */
    private void deactivateFallbackMode() {
        if (fallbackModeActive) {
            fallbackModeActive = false;
            log.info("Fallback mode DEACTIVATED - Redis queue healthy, resuming normal operations");
            incrementStat("fallbackDeactivations");
            setStat("fallbackDeactivatedAt", System.currentTimeMillis());
        }
    }

    // ==================== 任务扫描和救援 ====================

    /**
     * 扫描并救援视频翻译任务
     */
    private int scanAndRescueVideoTranslateTasks() {
        try {
            // 查询处理中但长时间未更新的任务（可能丢失在队列中）
            LocalDateTime staleThreshold = LocalDateTime.now().minusMinutes(10); // 10分钟未更新视为可能丢失

            List<VideoTranslateTaskPO> staleTasks = videoTranslateTaskMapper.selectList(
                new LambdaQueryWrapper<VideoTranslateTaskPO>()
                    .in(VideoTranslateTaskPO::getStatus, 0, 1) // 排队中、进行中
                    .lt(VideoTranslateTaskPO::getUpdateTime, staleThreshold)
                    .eq(VideoTranslateTaskPO::getIsDeleted, 0)
                    .last("LIMIT 50") // 批量处理，避免一次处理过多
            );

            int rescued = 0;
            for (VideoTranslateTaskPO task : staleTasks) {
                try {
                    // 构建TaskMessage并发送
                    TaskMessage taskMessage = TaskMessage.fallback(
                        task.getTaskId(),
                        task.getUserId(),
                        TaskType.VIDEO_TRANSLATE
                    ).addAttribute("taskContext", buildVideoTranslateContext(task));

                    boolean sent = messageSender.sendTaskMessage(taskMessage);

                    if (sent) {
                        rescued++;
                        log.debug("Rescued video translate task: taskId={}", task.getTaskId());
                    }
                } catch (Exception e) {
                    log.error("Failed to rescue video translate task: taskId={}", task.getTaskId(), e);
                }
            }

            if (rescued > 0) {
                log.info("Rescued {} video translate tasks", rescued);
            }
            return rescued;

        } catch (Exception e) {
            log.error("Failed to scan video translate tasks", e);
            return 0;
        }
    }

    /**
     * 扫描并救援音频生成任务
     */
    private int scanAndRescueAudioGenerationTasks() {
        try {
            LocalDateTime staleThreshold = LocalDateTime.now().minusMinutes(5); // 音频生成较快，5分钟阈值

            List<DigitalAudioTaskPO> staleTasks = digitalAudioTaskMapper.selectList(
                new LambdaQueryWrapper<DigitalAudioTaskPO>()
                    .in(DigitalAudioTaskPO::getStatus, 0, 1) // 排队中、进行中
                    .lt(DigitalAudioTaskPO::getUpdateTime, staleThreshold)
                    .eq(DigitalAudioTaskPO::getIsDeleted, false)
                    .last("LIMIT 50")
            );

            int rescued = 0;
            for (DigitalAudioTaskPO task : staleTasks) {
                try {
                    // 构建TaskMessage并发送
                    TaskMessage taskMessage = TaskMessage.fallback(
                        task.getTaskId(),
                        task.getUserId(),
                        TaskType.AUDIO_GENERATE
                    ).addAttribute("taskContext", buildAudioGenerateContext(task));

                    boolean sent = messageSender.sendTaskMessage(taskMessage);

                    if (sent) {
                        rescued++;
                        log.debug("Rescued audio generation task: taskId={}", task.getTaskId());
                    }
                } catch (Exception e) {
                    log.error("Failed to rescue audio generation task: taskId={}", task.getTaskId(), e);
                }
            }

            if (rescued > 0) {
                log.info("Rescued {} audio generation tasks", rescued);
            }
            return rescued;

        } catch (Exception e) {
            log.error("Failed to scan audio generation tasks", e);
            return 0;
        }
    }

    /**
     * 扫描并救援视频编辑任务
     */
    private int scanAndRescueVideoEditTasks() {
        try {
            LocalDateTime staleThreshold = LocalDateTime.now().minusMinutes(15); // 视频编辑较慢，15分钟阈值
            
            List<VideoEditTaskPO> staleTasks = videoEditTaskMapper.selectList(
                new LambdaQueryWrapper<VideoEditTaskPO>()
                    .in(VideoEditTaskPO::getStatus, 0, 1, 2) // 排队中、进行中、处理中
                    .lt(VideoEditTaskPO::getUpdateTime, staleThreshold)
                    .eq(VideoEditTaskPO::getIsDeleted, 0)
                    .last("LIMIT 50")
            );

            int rescued = 0;
            for (VideoEditTaskPO task : staleTasks) {
                try {
                    // 构建TaskMessage并发送
                    TaskMessage taskMessage = TaskMessage.fallback(
                        task.getTaskId(),
                        task.getUserId(),
                        TaskType.VIDEO_EDIT
                    ).addAttribute("taskContext", buildVideoEditContext(task));

                    boolean sent = messageSender.sendTaskMessage(taskMessage);

                    if (sent) {
                        rescued++;
                        log.debug("Rescued video edit task: taskId={}", task.getTaskId());
                    }
                } catch (Exception e) {
                    log.error("Failed to rescue video edit task: taskId={}", task.getTaskId(), e);
                }
            }

            if (rescued > 0) {
                log.info("Rescued {} video edit tasks", rescued);
            }
            return rescued;

        } catch (Exception e) {
            log.error("Failed to scan video edit tasks", e);
            return 0;
        }
    }

    // ==================== 超时任务处理 ====================

    /**
     * 处理视频翻译超时任务
     */
    private int processVideoTranslateTimeoutTasks() {
        try {
            // 视频翻译超时时间：60分钟
            LocalDateTime timeoutThreshold = LocalDateTime.now().minusMinutes(60);

            List<VideoTranslateTaskPO> timeoutTasks = videoTranslateTaskMapper.selectList(
                new LambdaQueryWrapper<VideoTranslateTaskPO>()
                    .in(VideoTranslateTaskPO::getStatus, 0, 1) // 排队中、进行中
                    .lt(VideoTranslateTaskPO::getCreatedTime, timeoutThreshold)
                    .eq(VideoTranslateTaskPO::getIsDeleted, 0)
                    .last("LIMIT 20")
            );

            int timeoutCount = 0;
            for (VideoTranslateTaskPO task : timeoutTasks) {
                try {
                    // 更新为超时状态
                    task.setStatus(4); // 超时状态
                    task.setErrorMsg("任务处理超时");
                    task.setUpdateTime(LocalDateTime.now());

                    videoTranslateTaskMapper.updateById(task);
                    timeoutCount++;

                    log.info("Marked video translate task as timeout: taskId={}", task.getTaskId());
                } catch (Exception e) {
                    log.error("Failed to mark video translate task as timeout: taskId={}", task.getTaskId(), e);
                }
            }

            return timeoutCount;

        } catch (Exception e) {
            log.error("Failed to process video translate timeout tasks", e);
            return 0;
        }
    }

    /**
     * 处理音频生成超时任务
     */
    private int processAudioGenerationTimeoutTasks() {
        try {
            // 音频生成超时时间：30分钟
            LocalDateTime timeoutThreshold = LocalDateTime.now().minusMinutes(30);

            List<DigitalAudioTaskPO> timeoutTasks = digitalAudioTaskMapper.selectList(
                new LambdaQueryWrapper<DigitalAudioTaskPO>()
                    .in(DigitalAudioTaskPO::getStatus, 0, 1) // 排队中、进行中
                    .lt(DigitalAudioTaskPO::getCreatedTime, timeoutThreshold)
                    .eq(DigitalAudioTaskPO::getIsDeleted, false)
                    .last("LIMIT 20")
            );

            int timeoutCount = 0;
            for (DigitalAudioTaskPO task : timeoutTasks) {
                try {
                    // 更新为超时状态
                    task.setStatus(4); // 超时状态
                    task.setErrorMsg("任务处理超时");
                    task.setUpdateTime(LocalDateTime.now());

                    digitalAudioTaskMapper.updateById(task);
                    timeoutCount++;

                    log.info("Marked audio generation task as timeout: taskId={}", task.getTaskId());
                } catch (Exception e) {
                    log.error("Failed to mark audio generation task as timeout: taskId={}", task.getTaskId(), e);
                }
            }

            return timeoutCount;

        } catch (Exception e) {
            log.error("Failed to process audio generation timeout tasks", e);
            return 0;
        }
    }

    /**
     * 处理视频编辑超时任务
     */
    private int processVideoEditTimeoutTasks() {
        try {
            // 视频编辑超时时间：90分钟
            LocalDateTime timeoutThreshold = LocalDateTime.now().minusMinutes(90);

            List<VideoEditTaskPO> timeoutTasks = videoEditTaskMapper.selectList(
                new LambdaQueryWrapper<VideoEditTaskPO>()
                    .in(VideoEditTaskPO::getStatus, 0, 1, 2) // 排队中、进行中、处理中
                    .lt(VideoEditTaskPO::getCreatedTime, timeoutThreshold)
                    .eq(VideoEditTaskPO::getIsDeleted, 0)
                    .last("LIMIT 20")
            );

            int timeoutCount = 0;
            for (VideoEditTaskPO task : timeoutTasks) {
                try {
                    // 更新为超时状态
                    task.setStatus(5); // 超时状态
                    task.setErrorMsg("任务处理超时");
                    task.setUpdateTime(new Date());

                    videoEditTaskMapper.updateById(task);
                    timeoutCount++;

                    log.info("Marked video edit task as timeout: taskId={}", task.getTaskId());
                } catch (Exception e) {
                    log.error("Failed to mark video edit task as timeout: taskId={}", task.getTaskId(), e);
                }
            }

            return timeoutCount;

        } catch (Exception e) {
            log.error("Failed to process video edit timeout tasks", e);
            return 0;
        }
    }

    // ==================== 任务上下文构建 ====================

    /**
     * 构建视频翻译任务上下文
     */
    private String buildVideoTranslateContext(VideoTranslateTaskPO task) {
        // 这里应该根据实际的VideoTranslateRequestDTO结构来构建
        // 暂时返回基本的JSON格式
        return String.format("{\"taskId\":\"%s\",\"userId\":\"%s\",\"sourceVideoUrl\":\"%s\",\"provider\":\"%s\"}",
                task.getTaskId(), task.getUserId(), task.getSourceVideoUrl(), task.getProvider());
    }

    /**
     * 构建音频生成任务上下文
     */
    private String buildAudioGenerateContext(DigitalAudioTaskPO task) {
        // 根据实际的AudioGenerationRequestDTO结构来构建
        return String.format("{\"taskId\":\"%s\",\"userId\":\"%s\",\"taskName\":\"%s\",\"provider\":\"%s\"}",
                task.getTaskId(), task.getUserId(), task.getTaskName(), task.getProvider());
    }

    /**
     * 构建视频编辑任务上下文
     */
    private String buildVideoEditContext(VideoEditTaskPO task) {
        // 根据实际的VideoEditRequestDTO结构来构建
        return String.format("{\"taskId\":\"%s\",\"userId\":\"%s\",\"taskName\":\"%s\"}",
                task.getTaskId(), task.getUserId(), task.getTaskName());
    }

    // ==================== 统计方法 ====================

    /**
     * 增加统计计数
     */
    private void incrementStat(String key) {
        fallbackStats.compute(key, (k, v) -> {
            if (v instanceof Long) {
                return ((Long) v) + 1;
            }
            return 1L;
        });
    }

    /**
     * 增加统计数值
     */
    private void addToStat(String key, long value) {
        fallbackStats.compute(key, (k, v) -> {
            if (v instanceof Long) {
                return ((Long) v) + value;
            }
            return value;
        });
    }

    /**
     * 设置统计值
     */
    private void setStat(String key, Object value) {
        fallbackStats.put(key, value);
    }

    /**
     * 获取兜底调度统计信息
     *
     * @return 统计信息
     */
    public Map<String, Object> getFallbackStatistics() {
        Map<String, Object> stats = new HashMap<>(fallbackStats);
        stats.put("startupTime", startupTime);
        stats.put("uptime", System.currentTimeMillis() - startupTime);
        stats.put("fallbackModeActive", fallbackModeActive);
        stats.put("lastRedisHealthy", lastRedisHealthy);

        return stats;
    }

    /**
     * 重置兜底调度统计
     */
    public void resetFallbackStatistics() {
        fallbackStats.clear();
        log.info("Fallback scheduler statistics reset");
    }
}
