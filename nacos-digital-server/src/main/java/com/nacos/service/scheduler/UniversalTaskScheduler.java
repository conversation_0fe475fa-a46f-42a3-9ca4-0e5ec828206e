package com.nacos.service.scheduler;

import com.nacos.entity.context.TaskContext;
import com.nacos.entity.dto.CallbackData;
import com.nacos.entity.dto.TaskMessage;
import com.nacos.entity.dto.TaskProcessResult;
import com.nacos.entity.enums.TaskType;
import com.nacos.entity.enums.UnifiedTaskStatusEnum;
import com.nacos.entity.po.DigitalAudioTaskPO;
import com.nacos.entity.po.VideoEditTaskPO;
import com.nacos.entity.po.VideoTranslateTaskPO;
import com.nacos.mapper.DigitalAudioTaskMapper;
import com.nacos.mapper.VideoEditTaskMapper;
import com.nacos.mapper.VideoTranslateTaskMapper;
import com.nacos.service.factory.TaskProcessorFactory;
import com.nacos.service.processor.UniversalTaskProcessor;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 通用任务调度器
 * 
 * 作为统一任务处理入口，负责：
 * 1. 任务调度和执行协调
 * 2. 处理器获取和任务上下文构建
 * 3. 任务状态管理和数据库更新
 * 4. 异步回调处理和错误恢复
 * 
 * 设计原则：
 * 1. 统一入口：所有任务处理都通过此调度器
 * 2. 类型安全：使用泛型确保参数类型安全
 * 3. 异常隔离：单个任务失败不影响其他任务
 * 4. 可观测性：完整的日志记录和监控支持
 * 
 * <AUTHOR>
 * @since 2025-07-31
 * @version 1.0 - 统一任务调度架构
 */
@Service
@Slf4j
public class UniversalTaskScheduler {

    @Autowired
    private TaskProcessorFactory processorFactory;

    @Autowired
    private VideoTranslateTaskMapper videoTranslateTaskMapper;

    @Autowired
    private DigitalAudioTaskMapper digitalAudioTaskMapper;

    @Autowired
    private VideoEditTaskMapper videoEditTaskMapper;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 任务执行统计
     */
    private final Map<String, Object> executionStats = new ConcurrentHashMap<>();

    /**
     * 调度器启动时间
     */
    private final long startupTime = System.currentTimeMillis();

    /**
     * 处理任务消息
     * 
     * 这是Redis消息队列的主要入口点，处理来自队列的任务消息
     * 
     * @param message 任务消息
     * @return 处理结果
     */
    @Transactional(rollbackFor = Exception.class)
    public TaskProcessResult processTaskMessage(TaskMessage message) {
        if (message == null) {
            throw new IllegalArgumentException("Task message cannot be null");
        }

        String taskId = message.getTaskId();
        TaskType taskType = message.getTaskType();
        
        log.info("Processing task message: taskId={}, taskType={}, userId={}, priority={}", 
                taskId, taskType, message.getUserId(), message.getPriority());

        try {
            // 更新执行统计
            updateExecutionStats("messageProcessed", 1);
            
            // 检查任务是否可以执行
            if (!message.isReadyToExecute()) {
                log.warn("Task message not ready to execute: taskId={}, scheduledTime={}", 
                        taskId, message.getScheduledTime());
                return TaskProcessResult.failure("Task not ready to execute");
            }

            // 处理任务
            TaskProcessResult result = processTask(taskId, taskType, message.getUserId());
            
            // 更新统计信息
            if (result.isSuccess()) {
                updateExecutionStats("successCount", 1);
            } else {
                updateExecutionStats("failureCount", 1);
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("Failed to process task message: taskId={}, taskType={}", 
                    taskId, taskType, e);
            updateExecutionStats("exceptionCount", 1);
            return TaskProcessResult.failure("Task processing failed: " + e.getMessage());
        }
    }

    /**
     * 处理指定任务
     * 
     * @param taskId 任务ID
     * @return 处理结果
     */
    @Transactional(rollbackFor = Exception.class)
    public TaskProcessResult processTask(String taskId) {
        if (taskId == null || taskId.trim().isEmpty()) {
            throw new IllegalArgumentException("Task ID cannot be null or empty");
        }

        log.info("Processing task: taskId={}", taskId);

        try {
            // 根据任务ID查询任务信息并确定任务类型
            TaskType taskType = determineTaskType(taskId);
            if (taskType == null) {
                log.error("Cannot determine task type for taskId: {}", taskId);
                return TaskProcessResult.failure("Cannot determine task type");
            }

            // 获取用户ID（这里需要根据实际情况从数据库查询）
            String userId = getUserIdByTaskId(taskId, taskType);
            
            return processTask(taskId, taskType, userId);
            
        } catch (Exception e) {
            log.error("Failed to process task: taskId={}", taskId, e);
            return TaskProcessResult.failure("Task processing failed: " + e.getMessage());
        }
    }

    /**
     * 处理指定任务（内部方法）
     * 
     * @param taskId   任务ID
     * @param taskType 任务类型
     * @param userId   用户ID
     * @return 处理结果
     */
    private TaskProcessResult processTask(String taskId, TaskType taskType, String userId) {
        LocalDateTime startTime = LocalDateTime.now();
        
        try {
            log.debug("Starting task processing: taskId={}, taskType={}, userId={}", 
                    taskId, taskType, userId);

            // 1. 检查处理器可用性
            if (!processorFactory.isProcessorAvailable(taskType)) {
                log.error("Processor not available for task type: {}", taskType);
                return TaskProcessResult.failure("Processor not available for task type: " + taskType);
            }

            // 2. 获取处理器
            UniversalTaskProcessor<Object> processor = processorFactory.getProcessor(taskType);
            log.debug("Retrieved processor: {} for task type: {}",
                    processor.getClass().getSimpleName(), taskType);

            // 3. 构建任务上下文
            TaskContext<Object> context = buildTaskContext(taskId, taskType, userId);
            if (context == null) {
                log.error("Failed to build task context for taskId: {}", taskId);
                return TaskProcessResult.failure("Failed to build task context");
            }

            // 4. 更新任务状态为进行中
            updateTaskStatus(taskId, taskType, UnifiedTaskStatusEnum.PROGRESS,
                           "Task processing started", startTime);

            // 5. 执行预处理
            processor.preProcess(context);

            // 6. 执行任务处理
            log.info("Executing task processing: taskId={}, processor={}",
                    taskId, processor.getClass().getSimpleName());

            TaskProcessResult result = processor.process(context);

            // 7. 执行后处理
            processor.postProcess(context, result);

            // 8. 更新任务状态
            LocalDateTime endTime = LocalDateTime.now();
            UnifiedTaskStatusEnum finalStatus = result.getStatus() != null ? 
                    result.getStatus() : (result.isSuccess() ? 
                    UnifiedTaskStatusEnum.SUCCESS : UnifiedTaskStatusEnum.FAILED);
            
            updateTaskStatus(taskId, taskType, finalStatus, result.getMessage(), endTime);

            // 9. 设置处理时间
            result.setProcessingTime(startTime, endTime);

            log.info("Task processing completed: taskId={}, status={}, success={}, message={}", 
                    taskId, finalStatus, result.isSuccess(), result.getMessage());

            return result;

        } catch (Exception e) {
            log.error("Task processing failed with exception: taskId={}, taskType={}", 
                    taskId, taskType, e);

            // 更新任务状态为失败
            try {
                updateTaskStatus(taskId, taskType, UnifiedTaskStatusEnum.FAILED, 
                               "Task processing failed: " + e.getMessage(), LocalDateTime.now());
            } catch (Exception updateException) {
                log.error("Failed to update task status after processing failure: taskId={}", 
                        taskId, updateException);
            }

            return TaskProcessResult.failure("Task processing failed: " + e.getMessage())
                    .addAttribute("exception", e.getClass().getSimpleName())
                    .addAttribute("stackTrace", getStackTrace(e));
        }
    }

    /**
     * 处理异步回调
     * 
     * @param taskId       任务ID
     * @param callbackData 回调数据
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleCallback(String taskId, CallbackData callbackData) {
        if (taskId == null || taskId.trim().isEmpty()) {
            throw new IllegalArgumentException("Task ID cannot be null or empty");
        }
        
        if (callbackData == null) {
            throw new IllegalArgumentException("Callback data cannot be null");
        }

        log.info("Handling callback: taskId={}, externalJobId={}, status={}, success={}", 
                taskId, callbackData.getExternalJobId(), callbackData.getStatus(), 
                callbackData.getSuccess());

        try {
            // 确定任务类型
            TaskType taskType = determineTaskType(taskId);
            if (taskType == null) {
                log.error("Cannot determine task type for callback: taskId={}", taskId);
                return;
            }

            // 获取处理器
            UniversalTaskProcessor<?> processor = processorFactory.getProcessor(taskType);
            
            // 委托给处理器处理回调
            processor.handleCallback(taskId, callbackData);
            
            // 更新统计信息
            updateExecutionStats("callbackProcessed", 1);
            
            log.info("Callback processed successfully: taskId={}, externalJobId={}", 
                    taskId, callbackData.getExternalJobId());

        } catch (Exception e) {
            log.error("Failed to handle callback: taskId={}, externalJobId={}", 
                    taskId, callbackData.getExternalJobId(), e);
            updateExecutionStats("callbackFailureCount", 1);
            
            // 回调处理失败不抛出异常，避免影响其他回调
        }
    }

    /**
     * 取消任务
     * 
     * @param taskId 任务ID
     * @return 是否成功取消
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelTask(String taskId) {
        if (taskId == null || taskId.trim().isEmpty()) {
            throw new IllegalArgumentException("Task ID cannot be null or empty");
        }

        log.info("Cancelling task: taskId={}", taskId);

        try {
            // 确定任务类型
            TaskType taskType = determineTaskType(taskId);
            if (taskType == null) {
                log.error("Cannot determine task type for cancellation: taskId={}", taskId);
                return false;
            }

            // 更新任务状态为已取消
            updateTaskStatus(taskId, taskType, UnifiedTaskStatusEnum.CANCELLED, 
                           "Task cancelled by user", LocalDateTime.now());

            // 尝试取消外部任务
            try {
                String externalJobId = getExternalJobId(taskId, taskType);
                if (externalJobId != null) {
                    UniversalTaskProcessor<?> processor = processorFactory.getProcessor(taskType);
                    boolean cancelled = processor.cancelExternalTask(externalJobId);
                    log.info("External task cancellation result: taskId={}, externalJobId={}, cancelled={}", 
                            taskId, externalJobId, cancelled);
                }
            } catch (Exception e) {
                log.warn("Failed to cancel external task: taskId={}", taskId, e);
                // 外部任务取消失败不影响本地状态更新
            }

            updateExecutionStats("cancelledCount", 1);
            log.info("Task cancelled successfully: taskId={}", taskId);
            return true;

        } catch (Exception e) {
            log.error("Failed to cancel task: taskId={}", taskId, e);
            return false;
        }
    }

    /**
     * 查询任务状态
     * 
     * @param taskId 任务ID
     * @return 任务状态信息
     */
    public Map<String, Object> queryTaskStatus(String taskId) {
        if (taskId == null || taskId.trim().isEmpty()) {
            throw new IllegalArgumentException("Task ID cannot be null or empty");
        }

        try {
            TaskType taskType = determineTaskType(taskId);
            if (taskType == null) {
                return createErrorStatusMap("Cannot determine task type");
            }

            // 根据任务类型查询状态
            return queryTaskStatusByType(taskId, taskType);

        } catch (Exception e) {
            log.error("Failed to query task status: taskId={}", taskId, e);
            return createErrorStatusMap("Failed to query task status: " + e.getMessage());
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 根据任务ID确定任务类型
     *
     * @param taskId 任务ID
     * @return 任务类型
     */
    private TaskType determineTaskType(String taskId) {
        if (taskId == null || taskId.trim().isEmpty()) {
            return null;
        }

        try {
            // 1. 先尝试根据任务ID前缀快速判断
            if (taskId.startsWith("VT_")) {
                return TaskType.VIDEO_TRANSLATE;
            } else if (taskId.startsWith("AG_")) {
                return TaskType.AUDIO_GENERATE;
            } else if (taskId.startsWith("VE_")) {
                return TaskType.VIDEO_EDIT;
            }

            // 2. 如果无法从前缀判断，查询数据库
            return queryTaskTypeFromDatabase(taskId);

        } catch (Exception e) {
            log.error("Failed to determine task type for taskId: {}", taskId, e);
            return null;
        }
    }

    /**
     * 从数据库查询任务类型
     *
     * @param taskId 任务ID
     * @return 任务类型
     */
    private TaskType queryTaskTypeFromDatabase(String taskId) {
        try {
            // 查询视频翻译任务表
            VideoTranslateTaskPO videoTranslateTask = videoTranslateTaskMapper.selectOne(
                    new LambdaQueryWrapper<VideoTranslateTaskPO>()
                            .eq(VideoTranslateTaskPO::getTaskId, taskId)
                            .eq(VideoTranslateTaskPO::getIsDeleted, 0)
                            .last("LIMIT 1")
            );
            if (videoTranslateTask != null) {
                return TaskType.VIDEO_TRANSLATE;
            }

            // 查询音频生成任务表
            DigitalAudioTaskPO audioTask = digitalAudioTaskMapper.selectOne(
                    new LambdaQueryWrapper<DigitalAudioTaskPO>()
                            .eq(DigitalAudioTaskPO::getTaskId, taskId)
                            .eq(DigitalAudioTaskPO::getIsDeleted, false)
                            .last("LIMIT 1")
            );
            if (audioTask != null) {
                return TaskType.AUDIO_GENERATE;
            }

            // 查询视频编辑任务表
            VideoEditTaskPO videoEditTask = videoEditTaskMapper.selectOne(
                    new LambdaQueryWrapper<VideoEditTaskPO>()
                            .eq(VideoEditTaskPO::getTaskId, taskId)
                            .eq(VideoEditTaskPO::getIsDeleted, 0)
                            .last("LIMIT 1")
            );
            if (videoEditTask != null) {
                return TaskType.VIDEO_EDIT;
            }

            log.warn("Task not found in any table: taskId={}", taskId);
            return null;

        } catch (Exception e) {
            log.error("Database query failed for task type determination: taskId={}", taskId, e);
            return null;
        }
    }

    /**
     * 根据任务ID和类型获取用户ID
     *
     * @param taskId   任务ID
     * @param taskType 任务类型
     * @return 用户ID
     */
    private String getUserIdByTaskId(String taskId, TaskType taskType) {
        try {
            switch (taskType) {
                case VIDEO_TRANSLATE:
                    VideoTranslateTaskPO videoTranslateTask = videoTranslateTaskMapper.selectOne(
                            new LambdaQueryWrapper<VideoTranslateTaskPO>()
                                    .eq(VideoTranslateTaskPO::getTaskId, taskId)
                                    .eq(VideoTranslateTaskPO::getIsDeleted, 0)
                                    .select(VideoTranslateTaskPO::getUserId)
                                    .last("LIMIT 1")
                    );
                    return videoTranslateTask != null ? videoTranslateTask.getUserId() : null;

                case AUDIO_GENERATE:
                    DigitalAudioTaskPO audioTask = digitalAudioTaskMapper.selectOne(
                            new LambdaQueryWrapper<DigitalAudioTaskPO>()
                                    .eq(DigitalAudioTaskPO::getTaskId, taskId)
                                    .eq(DigitalAudioTaskPO::getIsDeleted, false)
                                    .select(DigitalAudioTaskPO::getUserId)
                                    .last("LIMIT 1")
                    );
                    return audioTask != null ? audioTask.getUserId() : null;

                case VIDEO_EDIT:
                    VideoEditTaskPO videoEditTask = videoEditTaskMapper.selectOne(
                            new LambdaQueryWrapper<VideoEditTaskPO>()
                                    .eq(VideoEditTaskPO::getTaskId, taskId)
                                    .eq(VideoEditTaskPO::getIsDeleted, 0)
                                    .select(VideoEditTaskPO::getUserId)
                                    .last("LIMIT 1")
                    );
                    return videoEditTask != null ? videoEditTask.getUserId() : null;

                default:
                    log.warn("Unsupported task type for user ID query: {}", taskType);
                    return null;
            }
        } catch (Exception e) {
            log.error("Failed to query user ID: taskId={}, taskType={}", taskId, taskType, e);
            return null;
        }
    }

    /**
     * 构建任务上下文
     *
     * @param taskId   任务ID
     * @param taskType 任务类型
     * @param userId   用户ID
     * @return 任务上下文
     */
    private TaskContext<Object> buildTaskContext(String taskId, TaskType taskType, String userId) {
        try {
            switch (taskType) {
                case VIDEO_TRANSLATE:
                    return buildVideoTranslateContext(taskId, userId);
                case AUDIO_GENERATE:
                    return buildAudioGenerateContext(taskId, userId);
                case VIDEO_EDIT:
                    return buildVideoEditContext(taskId, userId);
                default:
                    log.error("Unsupported task type for context building: {}", taskType);
                    return null;
            }
        } catch (Exception e) {
            log.error("Failed to build task context: taskId={}, taskType={}, userId={}",
                    taskId, taskType, userId, e);
            return null;
        }
    }

    /**
     * 构建视频翻译任务上下文
     */
    private TaskContext<Object> buildVideoTranslateContext(String taskId, String userId) {
        VideoTranslateTaskPO taskPO = videoTranslateTaskMapper.selectOne(
                new LambdaQueryWrapper<VideoTranslateTaskPO>()
                        .eq(VideoTranslateTaskPO::getTaskId, taskId)
                        .eq(VideoTranslateTaskPO::getIsDeleted, 0)
                        .last("LIMIT 1")
        );

        if (taskPO == null) {
            log.error("Video translate task not found: taskId={}", taskId);
            return null;
        }

        // 解析参数JSON
        Object businessParams = parseJsonParams(taskPO.getRequestParamsJson());

        // 构建上下文
        TaskContext<Object> context = new TaskContext<>();
        context.setTaskId(taskId);
        context.setUserId(userId);
        context.setTaskType(TaskType.VIDEO_TRANSLATE);
        context.setBusinessParams(businessParams);

        // 设置元数据
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("taskPO", taskPO);
        metadata.put("createdTime", taskPO.getCreatedTime());
        metadata.put("status", taskPO.getStatus());
        context.setMetadata(metadata);

        return context;
    }

    /**
     * 构建音频生成任务上下文
     */
    private TaskContext<Object> buildAudioGenerateContext(String taskId, String userId) {
        DigitalAudioTaskPO taskPO = digitalAudioTaskMapper.selectOne(
                new LambdaQueryWrapper<DigitalAudioTaskPO>()
                        .eq(DigitalAudioTaskPO::getTaskId, taskId)
                        .eq(DigitalAudioTaskPO::getIsDeleted, false)
                        .last("LIMIT 1")
        );

        if (taskPO == null) {
            log.error("Audio generate task not found: taskId={}", taskId);
            return null;
        }

        // 解析参数JSON
        Object businessParams = parseJsonParams(taskPO.getRequestParamsJson());

        // 构建上下文
        TaskContext<Object> context = new TaskContext<>();
        context.setTaskId(taskId);
        context.setUserId(userId);
        context.setTaskType(TaskType.AUDIO_GENERATE);
        context.setBusinessParams(businessParams);

        // 设置元数据
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("taskPO", taskPO);
        metadata.put("createdTime", taskPO.getCreatedTime());
        metadata.put("status", taskPO.getStatus());
        context.setMetadata(metadata);

        return context;
    }

    /**
     * 构建视频编辑任务上下文
     */
    private TaskContext<Object> buildVideoEditContext(String taskId, String userId) {
        VideoEditTaskPO taskPO = videoEditTaskMapper.selectOne(
                new LambdaQueryWrapper<VideoEditTaskPO>()
                        .eq(VideoEditTaskPO::getTaskId, taskId)
                        .eq(VideoEditTaskPO::getIsDeleted, 0)
                        .last("LIMIT 1")
        );

        if (taskPO == null) {
            log.error("Video edit task not found: taskId={}", taskId);
            return null;
        }

        // 视频编辑任务的参数存储在子任务表中，这里构建一个基础的上下文
        // 具体的子任务参数将由处理器自行查询和处理
        Map<String, Object> businessParams = new HashMap<>();
        businessParams.put("mainTaskId", taskId);
        businessParams.put("totalCount", taskPO.getTotalCount());
        businessParams.put("successCount", taskPO.getSuccessCount());
        businessParams.put("failedCount", taskPO.getFailedCount());

        // 构建上下文
        TaskContext<Object> context = new TaskContext<>();
        context.setTaskId(taskId);
        context.setUserId(userId);
        context.setTaskType(TaskType.VIDEO_EDIT);
        context.setBusinessParams(businessParams);

        // 设置元数据
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("taskPO", taskPO);
        metadata.put("createdTime", taskPO.getCreatedTime());
        metadata.put("status", taskPO.getStatus());
        metadata.put("taskName", taskPO.getTaskName());
        metadata.put("taskDescription", taskPO.getTaskDescription());
        context.setMetadata(metadata);

        return context;
    }

    /**
     * 解析JSON参数
     */
    private Object parseJsonParams(String jsonParams) {
        if (jsonParams == null || jsonParams.trim().isEmpty()) {
            return new HashMap<>();
        }

        try {
            return objectMapper.readValue(jsonParams, Object.class);
        } catch (Exception e) {
            log.error("Failed to parse JSON parameters: {}", jsonParams, e);
            return new HashMap<>();
        }
    }

    /**
     * 更新任务状态
     *
     * @param taskId    任务ID
     * @param taskType  任务类型
     * @param status    新状态
     * @param message   状态消息
     * @param timestamp 时间戳
     */
    private void updateTaskStatus(String taskId, TaskType taskType, UnifiedTaskStatusEnum status,
                                 String message, LocalDateTime timestamp) {
        try {
            log.info("Updating task status: taskId={}, taskType={}, status={}, message={}, timestamp={}",
                    taskId, taskType, status, message, timestamp);

            switch (taskType) {
                case VIDEO_TRANSLATE:
                    updateVideoTranslateTaskStatus(taskId, status, message, timestamp);
                    break;
                case AUDIO_GENERATE:
                    updateAudioGenerateTaskStatus(taskId, status, message, timestamp);
                    break;
                case VIDEO_EDIT:
                    updateVideoEditTaskStatus(taskId, status, message, timestamp);
                    break;
                default:
                    log.warn("Unsupported task type for status update: {}", taskType);
            }
        } catch (Exception e) {
            log.error("Failed to update task status: taskId={}, taskType={}, status={}",
                    taskId, taskType, status, e);
        }
    }

    /**
     * 更新视频翻译任务状态
     */
    private void updateVideoTranslateTaskStatus(String taskId, UnifiedTaskStatusEnum status,
                                              String message, LocalDateTime timestamp) {
        VideoTranslateTaskPO updatePO = new VideoTranslateTaskPO();
        updatePO.setStatus(status.getCode());
        updatePO.setUpdateTime(timestamp);

        if (message != null) {
            // 根据状态设置相应的字段
            if (status == UnifiedTaskStatusEnum.FAILED) {
                updatePO.setErrorMsg(message);
            }
        }

        int updated = videoTranslateTaskMapper.update(updatePO,
                new LambdaQueryWrapper<VideoTranslateTaskPO>()
                        .eq(VideoTranslateTaskPO::getTaskId, taskId)
                        .eq(VideoTranslateTaskPO::getIsDeleted, 0)
        );

        if (updated > 0) {
            log.debug("Video translate task status updated: taskId={}, status={}", taskId, status);
        } else {
            log.warn("No video translate task updated: taskId={}", taskId);
        }
    }

    /**
     * 更新音频生成任务状态
     */
    private void updateAudioGenerateTaskStatus(String taskId, UnifiedTaskStatusEnum status,
                                             String message, LocalDateTime timestamp) {
        DigitalAudioTaskPO updatePO = new DigitalAudioTaskPO();
        updatePO.setStatus(status.getCode());
        updatePO.setUpdateTime(timestamp);

        if (message != null) {
            if (status == UnifiedTaskStatusEnum.FAILED) {
                updatePO.setErrorMsg(message);
            }
        }

        int updated = digitalAudioTaskMapper.update(updatePO,
                new LambdaQueryWrapper<DigitalAudioTaskPO>()
                        .eq(DigitalAudioTaskPO::getTaskId, taskId)
                        .eq(DigitalAudioTaskPO::getIsDeleted, false)
        );

        if (updated > 0) {
            log.debug("Audio generate task status updated: taskId={}, status={}", taskId, status);
        } else {
            log.warn("No audio generate task updated: taskId={}", taskId);
        }
    }

    /**
     * 更新视频编辑任务状态
     */
    private void updateVideoEditTaskStatus(String taskId, UnifiedTaskStatusEnum status,
                                         String message, LocalDateTime timestamp) {
        VideoEditTaskPO updatePO = new VideoEditTaskPO();
        updatePO.setStatus(status.getCode());
        // 转换LocalDateTime为Date
        updatePO.setUpdateTime(Date.from(timestamp.atZone(ZoneId.systemDefault()).toInstant()));

        if (message != null) {
            if (status == UnifiedTaskStatusEnum.FAILED) {
                updatePO.setErrorMsg(message);
            }
        }

        int updated = videoEditTaskMapper.update(updatePO,
                new LambdaQueryWrapper<VideoEditTaskPO>()
                        .eq(VideoEditTaskPO::getTaskId, taskId)
                        .eq(VideoEditTaskPO::getIsDeleted, 0)
        );

        if (updated > 0) {
            log.debug("Video edit task status updated: taskId={}, status={}", taskId, status);
        } else {
            log.warn("No video edit task updated: taskId={}", taskId);
        }
    }

    /**
     * 获取外部任务ID
     *
     * @param taskId   任务ID
     * @param taskType 任务类型
     * @return 外部任务ID
     */
    private String getExternalJobId(String taskId, TaskType taskType) {
        try {
            switch (taskType) {
                case VIDEO_TRANSLATE:
                    // 视频翻译任务的外部任务ID存储在requestParamsJson中或者通过其他方式获取
                    // 这里返回任务ID作为标识，具体的外部任务ID由处理器管理
                    log.debug("Video translate task external job ID should be managed by processor: taskId={}", taskId);
                    return taskId;

                case AUDIO_GENERATE:
                    // 音频生成任务的外部任务ID存储在requestParamsJson中或者通过其他方式获取
                    // 这里返回任务ID作为标识，具体的外部任务ID由处理器管理
                    log.debug("Audio generate task external job ID should be managed by processor: taskId={}", taskId);
                    return taskId;

                case VIDEO_EDIT:
                    // 视频编辑任务的外部任务ID存储在子任务表中，这里返回主任务ID
                    // 具体的外部任务ID需要通过子任务查询
                    log.debug("Video edit task external job ID should be queried from sub-tasks: taskId={}", taskId);
                    return taskId; // 返回主任务ID作为标识

                default:
                    log.warn("Unsupported task type for external job ID query: {}", taskType);
                    return null;
            }
        } catch (Exception e) {
            log.error("Failed to query external job ID: taskId={}, taskType={}", taskId, taskType, e);
            return null;
        }
    }

    /**
     * 根据任务类型查询任务状态
     *
     * @param taskId   任务ID
     * @param taskType 任务类型
     * @return 状态信息
     */
    public Map<String, Object> queryTaskStatusByType(String taskId, TaskType taskType) {
        try {
            switch (taskType) {
                case VIDEO_TRANSLATE:
                    return queryVideoTranslateTaskStatus(taskId);
                case AUDIO_GENERATE:
                    return queryAudioGenerateTaskStatus(taskId);
                case VIDEO_EDIT:
                    return queryVideoEditTaskStatus(taskId);
                default:
                    log.warn("Unsupported task type for status query: {}", taskType);
                    Map<String, Object> unknownStatus = new HashMap<>();
                    unknownStatus.put("taskId", taskId);
                    unknownStatus.put("taskType", taskType);
                    unknownStatus.put("status", "unknown");
                    unknownStatus.put("error", "Unsupported task type");
                    return unknownStatus;
            }
        } catch (Exception e) {
            log.error("Failed to query task status: taskId={}, taskType={}", taskId, taskType, e);
            Map<String, Object> errorStatus = new HashMap<>();
            errorStatus.put("taskId", taskId);
            errorStatus.put("taskType", taskType);
            errorStatus.put("status", "error");
            errorStatus.put("error", e.getMessage());
            return errorStatus;
        }
    }

    /**
     * 创建错误状态映射
     * 
     * @param errorMessage 错误消息
     * @return 错误状态映射
     */
    private Map<String, Object> createErrorStatusMap(String errorMessage) {
        Map<String, Object> errorStatus = new HashMap<>();
        errorStatus.put("error", true);
        errorStatus.put("message", errorMessage);
        errorStatus.put("timestamp", LocalDateTime.now());
        return errorStatus;
    }

    /**
     * 更新执行统计
     *
     * @param key   统计键
     * @param delta 增量
     */
    private void updateExecutionStats(String key, long delta) {
        executionStats.merge(key, delta, (oldValue, newValue) ->
                ((Number) oldValue).longValue() + ((Number) newValue).longValue());
    }

    /**
     * 增加执行统计计数
     *
     * @param key 统计键
     */
    private void incrementExecutionStat(String key) {
        updateExecutionStats(key, 1);
    }

    /**
     * 获取异常堆栈信息
     * 
     * @param throwable 异常
     * @return 堆栈信息字符串
     */
    private String getStackTrace(Throwable throwable) {
        java.io.StringWriter sw = new java.io.StringWriter();
        java.io.PrintWriter pw = new java.io.PrintWriter(sw);
        throwable.printStackTrace(pw);
        return sw.toString();
    }

    // ==================== 公共查询方法 ====================

    /**
     * 获取调度器统计信息
     * 
     * @return 统计信息
     */
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>(executionStats);
        stats.put("startupTime", startupTime);
        stats.put("uptime", System.currentTimeMillis() - startupTime);
        stats.put("processorFactoryStats", processorFactory.getStatistics());
        return stats;
    }

    /**
     * 查询视频翻译任务状态
     */
    private Map<String, Object> queryVideoTranslateTaskStatus(String taskId) {
        VideoTranslateTaskPO taskPO = videoTranslateTaskMapper.selectOne(
                new LambdaQueryWrapper<VideoTranslateTaskPO>()
                        .eq(VideoTranslateTaskPO::getTaskId, taskId)
                        .eq(VideoTranslateTaskPO::getIsDeleted, 0)
                        .last("LIMIT 1")
        );

        Map<String, Object> status = new HashMap<>();
        status.put("taskId", taskId);
        status.put("taskType", TaskType.VIDEO_TRANSLATE);

        if (taskPO != null) {
            status.put("status", taskPO.getStatus());
            status.put("statusDescription", taskPO.getStatusDescription());
            status.put("errorMsg", taskPO.getErrorMsg());
            status.put("createdTime", taskPO.getCreatedTime());
            status.put("updateTime", taskPO.getUpdateTime());
            status.put("translatedVideoUrl", taskPO.getTranslatedVideoUrl());
            status.put("translatedAudioUrl", taskPO.getTranslatedAudioUrl());
            status.put("durationMs", taskPO.getDurationMs());
        } else {
            status.put("status", "not_found");
            status.put("error", "Task not found");
        }

        return status;
    }

    /**
     * 查询音频生成任务状态
     */
    private Map<String, Object> queryAudioGenerateTaskStatus(String taskId) {
        DigitalAudioTaskPO taskPO = digitalAudioTaskMapper.selectOne(
                new LambdaQueryWrapper<DigitalAudioTaskPO>()
                        .eq(DigitalAudioTaskPO::getTaskId, taskId)
                        .eq(DigitalAudioTaskPO::getIsDeleted, false)
                        .last("LIMIT 1")
        );

        Map<String, Object> status = new HashMap<>();
        status.put("taskId", taskId);
        status.put("taskType", TaskType.AUDIO_GENERATE);

        if (taskPO != null) {
            status.put("status", taskPO.getStatus());
            status.put("errorMsg", taskPO.getErrorMsg());
            status.put("createdTime", taskPO.getCreatedTime());
            status.put("updateTime", taskPO.getUpdateTime());
            status.put("generatedAudioUrl", taskPO.getGeneratedAudioUrl());
            status.put("durationMs", taskPO.getDurationMs());
            status.put("provider", taskPO.getProvider());
        } else {
            status.put("status", "not_found");
            status.put("error", "Task not found");
        }

        return status;
    }

    /**
     * 查询视频编辑任务状态
     */
    private Map<String, Object> queryVideoEditTaskStatus(String taskId) {
        VideoEditTaskPO taskPO = videoEditTaskMapper.selectOne(
                new LambdaQueryWrapper<VideoEditTaskPO>()
                        .eq(VideoEditTaskPO::getTaskId, taskId)
                        .eq(VideoEditTaskPO::getIsDeleted, 0)
                        .last("LIMIT 1")
        );

        Map<String, Object> status = new HashMap<>();
        status.put("taskId", taskId);
        status.put("taskType", TaskType.VIDEO_EDIT);

        if (taskPO != null) {
            status.put("status", taskPO.getStatus());
            status.put("errorMsg", taskPO.getErrorMsg());
            status.put("createdTime", taskPO.getCreatedTime());
            status.put("updateTime", taskPO.getUpdateTime());
            status.put("outputVideoUrl", taskPO.getOutputVideoUrl());
            status.put("outputCoverUrl", taskPO.getOutputCoverUrl());
            status.put("totalCount", taskPO.getTotalCount());
            status.put("successCount", taskPO.getSuccessCount());
            status.put("failedCount", taskPO.getFailedCount());
            status.put("progress", taskPO.getProgress());
            status.put("cost", taskPO.getCost());
        } else {
            status.put("status", "not_found");
            status.put("error", "Task not found");
        }

        return status;
    }

    /**
     * 获取调度器状态
     *
     * @return 状态信息
     */
    public Map<String, Object> getStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("active", true);
        status.put("startupTime", startupTime);
        status.put("uptime", System.currentTimeMillis() - startupTime);
        status.put("processorFactoryStatus", processorFactory.getStatus());
        status.put("executionStats", new HashMap<>(executionStats));
        return status;
    }

    // ==================== 扩展方法 ====================



    /**
     * 获取用户任务列表
     *
     * @param userId   用户ID
     * @param taskType 任务类型（可选）
     * @param status   任务状态（可选）
     * @param pageNum  页码
     * @param pageSize 页大小
     * @return 用户任务列表
     */
    public Map<String, Object> getUserTasks(String userId, TaskType taskType,
                                          UnifiedTaskStatusEnum status,
                                          Integer pageNum, Integer pageSize) {
        log.debug("Getting user tasks: userId={}, taskType={}, status={}, pageNum={}, pageSize={}",
                 userId, taskType, status, pageNum, pageSize);

        try {
            Map<String, Object> result = new HashMap<>();
            List<Map<String, Object>> tasks = new ArrayList<>();
            int totalCount = 0;

            // 根据任务类型查询
            if (taskType != null) {
                switch (taskType) {
                    case VIDEO_TRANSLATE:
                        Map<String, Object> videoTasks = getUserVideoTranslateTasks(userId, status, pageNum, pageSize);
                        tasks.addAll((List<Map<String, Object>>) videoTasks.getOrDefault("tasks", Collections.emptyList()));
                        totalCount += (Integer) videoTasks.getOrDefault("totalCount", 0);
                        break;
                    case AUDIO_GENERATE:
                        Map<String, Object> audioTasks = getUserAudioGenerateTasks(userId, status, pageNum, pageSize);
                        tasks.addAll((List<Map<String, Object>>) audioTasks.getOrDefault("tasks", Collections.emptyList()));
                        totalCount += (Integer) audioTasks.getOrDefault("totalCount", 0);
                        break;
                    case VIDEO_EDIT:
                        Map<String, Object> editTasks = getUserVideoEditTasks(userId, status, pageNum, pageSize);
                        tasks.addAll((List<Map<String, Object>>) editTasks.getOrDefault("tasks", Collections.emptyList()));
                        totalCount += (Integer) editTasks.getOrDefault("totalCount", 0);
                        break;
                }
            } else {
                // 查询所有类型的任务
                for (TaskType type : TaskType.values()) {
                    Map<String, Object> typeTasks = getUserTasks(userId, type, status, pageNum, pageSize);
                    tasks.addAll((List<Map<String, Object>>) typeTasks.getOrDefault("tasks", Collections.emptyList()));
                    totalCount += (Integer) typeTasks.getOrDefault("totalCount", 0);
                }
            }

            // 按创建时间倒序排序
            tasks.sort((a, b) -> {
                Object timeA = a.get("createTime");
                Object timeB = b.get("createTime");
                if (timeA instanceof Date && timeB instanceof Date) {
                    return ((Date) timeB).compareTo((Date) timeA);
                }
                return 0;
            });

            // 分页处理
            int startIndex = (pageNum - 1) * pageSize;
            int endIndex = Math.min(startIndex + pageSize, tasks.size());
            if (startIndex < tasks.size()) {
                tasks = tasks.subList(startIndex, endIndex);
            } else {
                tasks = Collections.emptyList();
            }

            result.put("tasks", tasks);
            result.put("totalCount", totalCount);
            result.put("pageNum", pageNum);
            result.put("pageSize", pageSize);
            result.put("totalPages", (totalCount + pageSize - 1) / pageSize);

            return result;

        } catch (Exception e) {
            log.error("Failed to get user tasks: userId={}", userId, e);
            return Collections.emptyMap();
        }
    }

    /**
     * 获取用户任务统计
     *
     * @param userId 用户ID
     * @return 用户任务统计信息
     */
    public Map<String, Object> getUserTaskStatistics(String userId) {
        log.debug("Getting user task statistics: userId={}", userId);

        try {
            Map<String, Object> statistics = new HashMap<>();

            // 统计各类型任务数量
            for (TaskType taskType : TaskType.values()) {
                Map<String, Object> typeStats = getUserTaskStatisticsByType(userId, taskType);
                statistics.put(taskType.getCode().toLowerCase() + "Stats", typeStats);
            }

            // 计算总体统计
            int totalTasks = 0;
            int totalSuccess = 0;
            int totalFailed = 0;
            int totalInProgress = 0;

            for (TaskType taskType : TaskType.values()) {
                String key = taskType.getCode().toLowerCase() + "Stats";
                Map<String, Object> typeStats = (Map<String, Object>) statistics.get(key);
                if (typeStats != null) {
                    totalTasks += (Integer) typeStats.getOrDefault("totalCount", 0);
                    totalSuccess += (Integer) typeStats.getOrDefault("successCount", 0);
                    totalFailed += (Integer) typeStats.getOrDefault("failedCount", 0);
                    totalInProgress += (Integer) typeStats.getOrDefault("inProgressCount", 0);
                }
            }

            statistics.put("totalTasks", totalTasks);
            statistics.put("totalSuccess", totalSuccess);
            statistics.put("totalFailed", totalFailed);
            statistics.put("totalInProgress", totalInProgress);

            // 计算成功率
            if (totalTasks > 0) {
                double successRate = (double) totalSuccess / totalTasks * 100;
                statistics.put("successRate", String.format("%.2f%%", successRate));
            } else {
                statistics.put("successRate", "N/A");
            }

            return statistics;

        } catch (Exception e) {
            log.error("Failed to get user task statistics: userId={}", userId, e);
            return Collections.emptyMap();
        }
    }

    // ==================== 辅助方法 ====================

    /**
     * 获取用户视频翻译任务
     */
    private Map<String, Object> getUserVideoTranslateTasks(String userId, UnifiedTaskStatusEnum status, Integer pageNum, Integer pageSize) {
        // 简化实现，返回空结果
        Map<String, Object> result = new HashMap<>();
        result.put("tasks", Collections.emptyList());
        result.put("totalCount", 0);
        return result;
    }

    /**
     * 获取用户音频生成任务
     */
    private Map<String, Object> getUserAudioGenerateTasks(String userId, UnifiedTaskStatusEnum status, Integer pageNum, Integer pageSize) {
        // 简化实现，返回空结果
        Map<String, Object> result = new HashMap<>();
        result.put("tasks", Collections.emptyList());
        result.put("totalCount", 0);
        return result;
    }

    /**
     * 获取用户视频编辑任务
     */
    private Map<String, Object> getUserVideoEditTasks(String userId, UnifiedTaskStatusEnum status, Integer pageNum, Integer pageSize) {
        // 简化实现，返回空结果
        Map<String, Object> result = new HashMap<>();
        result.put("tasks", Collections.emptyList());
        result.put("totalCount", 0);
        return result;
    }

    /**
     * 获取用户任务统计（按类型）
     */
    private Map<String, Object> getUserTaskStatisticsByType(String userId, TaskType taskType) {
        // 简化实现，返回空统计
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalCount", 0);
        stats.put("successCount", 0);
        stats.put("failedCount", 0);
        stats.put("inProgressCount", 0);
        return stats;
    }

    /**
     * 获取系统任务统计（按类型）
     */
    private Map<String, Object> getSystemTaskStatisticsByType(TaskType taskType) {
        // 简化实现，返回空统计
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalCount", 0);
        stats.put("successCount", 0);
        stats.put("failedCount", 0);
        stats.put("inProgressCount", 0);
        return stats;
    }

    /**
     * 清理指定类型的过期任务
     */
    private int cleanupExpiredTasksByType(TaskType taskType, Date cutoffDate) {
        // 简化实现，返回0
        return 0;
    }

    /**
     * 检查调度器健康状态
     *
     * @return 是否健康
     */
    public boolean isHealthy() {
        try {
            // 检查处理器工厂状态
            if (processorFactory == null) {
                return false;
            }

            Map<String, Object> factoryStatus = processorFactory.getStatus();
            Boolean factoryHealthy = (Boolean) factoryStatus.get("healthy");

            return factoryHealthy != null && factoryHealthy;

        } catch (Exception e) {
            log.error("Failed to check scheduler health", e);
            return false;
        }
    }

    /**
     * 刷新处理器缓存
     *
     * @return 是否刷新成功
     */
    public boolean refreshProcessorCache() {
        try {
            log.info("Refreshing processor cache");

            // 刷新处理器工厂缓存
            if (processorFactory != null) {
                processorFactory.refreshCache();
                log.info("Processor cache refreshed successfully");
                return true;
            }

            log.warn("Processor factory is null, cannot refresh cache");
            return false;

        } catch (Exception e) {
            log.error("Failed to refresh processor cache", e);
            return false;
        }
    }



    /**
     * 获取系统任务统计
     *
     * @param taskType 任务类型（可选）
     * @return 系统任务统计信息
     */
    public Map<String, Object> getSystemTaskStatistics(TaskType taskType) {
        log.debug("Getting system task statistics: taskType={}", taskType);

        try {
            Map<String, Object> statistics = new HashMap<>();

            if (taskType != null) {
                // 获取指定类型的统计
                Map<String, Object> typeStats = getSystemTaskStatisticsByType(taskType);
                statistics.put(taskType.getCode().toLowerCase() + "Stats", typeStats);
            } else {
                // 获取所有类型的统计
                for (TaskType type : TaskType.values()) {
                    Map<String, Object> typeStats = getSystemTaskStatisticsByType(type);
                    statistics.put(type.getCode().toLowerCase() + "Stats", typeStats);
                }
            }

            // 添加调度器统计信息
            statistics.put("schedulerStats", getStatistics());
            statistics.put("timestamp", System.currentTimeMillis());

            return statistics;

        } catch (Exception e) {
            log.error("Failed to get system task statistics: taskType={}", taskType, e);
            return Collections.emptyMap();
        }
    }

    /**
     * 重试任务
     *
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return 是否重试成功
     */
    public boolean retryTask(String taskId, String userId) {
        log.info("Retrying task: taskId={}, userId={}", taskId, userId);

        try {
            // 查询任务信息
            Map<String, Object> taskInfo = queryTaskStatus(taskId);
            if (taskInfo == null || taskInfo.isEmpty()) {
                log.warn("Task not found for retry: taskId={}", taskId);
                return false;
            }

            // 检查任务状态是否可以重试
            Object statusObj = taskInfo.get("status");
            if (statusObj instanceof Integer) {
                UnifiedTaskStatusEnum status = UnifiedTaskStatusEnum.fromCode((Integer) statusObj);
                if (status != UnifiedTaskStatusEnum.FAILED && status != UnifiedTaskStatusEnum.TIMEOUT) {
                    log.warn("Task cannot be retried in current status: taskId={}, status={}", taskId, status);
                    return false;
                }
            }

            // 重置任务状态为排队中
            Object taskTypeObj = taskInfo.get("taskType");
            if (taskTypeObj instanceof String) {
                TaskType taskType = TaskType.fromCode((String) taskTypeObj);
                if (taskType != null) {
                    updateTaskStatus(taskId, taskType, UnifiedTaskStatusEnum.QUEUING, "任务重试", LocalDateTime.now());
                    incrementExecutionStat("totalRetried");
                    return true;
                }
            }

            return false;

        } catch (Exception e) {
            log.error("Failed to retry task: taskId={}, userId={}", taskId, userId, e);
            return false;
        }
    }

    /**
     * 清理过期任务
     *
     * @param beforeDays 保留天数
     * @param taskType   任务类型（可选）
     * @return 清理结果
     */
    public Map<String, Object> cleanupExpiredTasks(Integer beforeDays, TaskType taskType) {
        log.info("Cleaning up expired tasks: beforeDays={}, taskType={}", beforeDays, taskType);

        try {
            Map<String, Object> result = new HashMap<>();
            int totalCleaned = 0;

            // 计算截止时间
            LocalDateTime cutoffTime = LocalDateTime.now().minusDays(beforeDays);
            Date cutoffDate = Date.from(cutoffTime.atZone(ZoneId.systemDefault()).toInstant());

            if (taskType != null) {
                // 清理指定类型的过期任务
                int cleaned = cleanupExpiredTasksByType(taskType, cutoffDate);
                totalCleaned += cleaned;
                result.put(taskType.getCode().toLowerCase() + "Cleaned", cleaned);
            } else {
                // 清理所有类型的过期任务
                for (TaskType type : TaskType.values()) {
                    int cleaned = cleanupExpiredTasksByType(type, cutoffDate);
                    totalCleaned += cleaned;
                    result.put(type.getCode().toLowerCase() + "Cleaned", cleaned);
                }
            }

            result.put("totalCleaned", totalCleaned);
            result.put("cutoffDate", cutoffDate);
            result.put("beforeDays", beforeDays);

            log.info("Expired tasks cleanup completed: totalCleaned={}", totalCleaned);
            return result;

        } catch (Exception e) {
            log.error("Failed to cleanup expired tasks: beforeDays={}, taskType={}", beforeDays, taskType, e);
            return Collections.emptyMap();
        }
    }
}
