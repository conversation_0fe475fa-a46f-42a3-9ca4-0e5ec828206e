package com.nacos.task;

import com.nacos.service.DigitalVideoService;
import com.nacos.service.TxMiniService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 视频任务定时处理器
 *
 * @deprecated 已迁移到统一任务系统架构，请使用 {@link com.nacos.service.scheduler.UniversalTaskScheduler}
 */
@Deprecated(since = "2025-07-31", forRemoval = true)
@Slf4j
@Component
@RequiredArgsConstructor
public class TxMIniTaskScheduler {

    private final TxMiniService txMiniService;

    /**
     * 每30秒执行一次，处理排队中的任务，老的騰訊任務
     */
//    @Scheduled(fixedRate = 5000)
    public void processQueueingTasks() {
        try {
            txMiniService.processVideo();
            txMiniService.processVoice();
            txMiniService.processFinish();
        } catch (Exception e) {
            log.error("处理排队中任务异常", e);
        }
    }



} 