package com.nacos.task;

import com.nacos.service.DigitalVideoService;
import com.nacos.service.VideoEditService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 视频任务定时处理器
 *
 * @deprecated 已迁移到统一任务系统架构，请使用 {@link com.nacos.service.scheduler.UniversalTaskScheduler}
 */
@Deprecated(since = "2025-07-31", forRemoval = true)
@Slf4j
@Component
@RequiredArgsConstructor
public class VideoTaskScheduler {

    private final DigitalVideoService digitalVideoService;
    private final VideoEditService videoEditService;

    /**
     * 每30秒执行一次，处理排队中的任务，老的騰訊任務
     */
//    @Scheduled(fixedRate = 30000)
    public void processQueueingTasks() {
        try {
            digitalVideoService.processQueueingTasks();
        } catch (Exception e) {
            log.error("处理排队中任务异常", e);
        }
    }

    /**
     * 每15秒执行一次，处理视频编辑排队中的任务
     */
    @Scheduled(fixedRate = 15000)
    public void processVideoEditQueueingTasks() {
        try {
            videoEditService.processQueueingTasks();
        } catch (Exception e) {
            log.error("处理视频编辑排队中任务异常", e);
        }
    }

    /**
     * 每10秒执行一次，检查进行中的视频编辑任务状态
     * 用于轮询禅境API状态，确保状态码10（生成中）能继续被检查
     */
    @Scheduled(fixedRate = 10000)
    public void processVideoEditInProgressTasks() {
        try {
            videoEditService.processInProgressTasks();
        } catch (Exception e) {
            log.error("检查视频编辑进行中任务状态异常", e);
        }
    }

    /**
     * 每5分钟执行一次，处理超时的视频编辑任务
     */
    @Scheduled(fixedRate = 300000)
    public void processVideoEditTimeoutTasks() {
        try {
            videoEditService.processTimeoutTasks();
        } catch (Exception e) {
            log.error("处理视频编辑超时任务异常", e);
        }
    }

}