package com.nacos.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nacos.config.VideoTranslateConfig;
import com.nacos.entity.po.VideoTranslateTaskPO;
import com.nacos.mapper.VideoTranslateTaskMapper;
import com.nacos.service.VideoTranslateAsyncService;
import com.nacos.service.processor.VideoTranslateProcessor;
import com.nacos.service.processor.VideoTranslateProcessorFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 视频翻译任务定时调度器
 * 负责定时处理视频翻译任务的状态同步、超时检测等
 * 支持多服务商的任务调度和健康检查
 *
 * @deprecated 已迁移到统一任务系统架构，请使用 {@link com.nacos.service.scheduler.UniversalTaskScheduler}
 * <AUTHOR>
 * @since 2025-01-29
 * @version 2.0 - 支持多服务商
 */
@Deprecated(since = "2025-07-31", forRemoval = true)
@Component
@Slf4j
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "digital.video.translate.scheduler", name = "enabled", havingValue = "false", matchIfMissing = false)
public class VideoTranslateTaskScheduler {

    private final VideoTranslateAsyncService videoTranslateAsyncService;
    private final VideoTranslateProcessorFactory processorFactory;
    private final VideoTranslateConfig config;

    /**
     * 视频翻译任务数据访问层
     */
    @Autowired
    private VideoTranslateTaskMapper videoTranslateTaskMapper;

    /**
     * 统一任务系统开关
     */
    @Value("${universal-task.enabled:false}")
    private boolean useUniversalTask;

    /**
     * 兜底机制开关
     */
    @Value("${universal-task.fallback-scheduler.enabled:true}")
    private boolean fallbackEnabled;

    /**
     * 定时处理排队中的翻译任务
     * 支持多服务商的任务分组处理
     * 执行频率：每30秒执行一次
     *
     * 优化后的逻辑：
     * 1. 检查系统健康状态
     * 2. 验证处理器可用性
     * 3. 批量处理submitted状态的任务
     * 4. 监控处理结果和异常
     */
    @Scheduled(fixedRateString = "${digital.video.translate.scheduler.queue-process-rate:30000}")
    public void processQueueingTasks() {
        String methodName = "processQueueingTasks";
        try {
            // 检查统一任务系统配置
            if (useUniversalTask && !fallbackEnabled) {
                log.debug("[{}] 统一任务系统已启用，跳过定时任务处理", methodName);
                return;
            }

            if (useUniversalTask && fallbackEnabled) {
                log.debug("[{}] 统一任务系统已启用，执行兜底机制", methodName);
                processFailoverTasks();
                return;
            }

            log.debug("[{}] 开始处理排队中的视频翻译任务...", methodName);

            // 1. 检查是否有可用的处理器
            List<VideoTranslateProcessor> healthyProcessors = processorFactory.getHealthyProcessors();
            if (healthyProcessors.isEmpty()) {
                log.warn("[{}] 没有可用的视频翻译处理器，跳过排队任务处理", methodName);
                return;
            }

            // 2. 记录可用的服务商和配置信息
            Set<String> availableProviders = processorFactory.getEnabledProviders();
            log.debug("[{}] 当前可用的服务商: {}, 健康处理器数量: {}",
                     methodName, availableProviders, healthyProcessors.size());

            // 3. 记录系统负载信息
            int globalTimeoutMinutes = config.getGlobalTimeoutMinutes();
            log.debug("[{}] 系统配置 - 全局超时时间: {}分钟", methodName, globalTimeoutMinutes);

            // 4. 处理排队任务（submitted状态的任务）
            long startTime = System.currentTimeMillis();
            videoTranslateAsyncService.processQueueingTasks();
            long processingTime = System.currentTimeMillis() - startTime;

            log.debug("[{}] 排队任务处理完成，耗时: {}ms", methodName, processingTime);

        } catch (Exception e) {
            log.error("[{}] 处理排队任务异常: {}", methodName, e.getMessage(), e);
        }
    }

    /**
     * 定时处理超时的翻译任务
     * 支持服务商特定的超时设置
     * 执行频率：每2分钟执行一次
     *
     * 优化后的逻辑：
     * 1. 检查各服务商的超时配置
     * 2. 批量处理超时任务
     * 3. 记录超时统计信息
     * 4. 监控超时处理性能
     */
    @Scheduled(fixedRateString = "${digital.video.translate.scheduler.timeout-check-rate:120000}")
    public void processTimeoutTasks() {
        String methodName = "processTimeoutTasks";
        try {
            // 检查统一任务系统配置 - 超时检查无论如何都要执行
            if (useUniversalTask && !fallbackEnabled) {
                log.debug("[{}] 统一任务系统已启用且兜底机制禁用，跳过超时任务检查", methodName);
                return;
            }

            log.debug("[{}] 开始检查超时的视频翻译任务...", methodName);

            // 1. 记录全局超时配置
            int globalTimeoutMinutes = config.getGlobalTimeoutMinutes();
            log.debug("[{}] 全局超时时间设置: {}分钟", methodName, globalTimeoutMinutes);

            // 2. 记录各服务商的超时配置
            Map<String, VideoTranslateConfig.ProviderConfig> providers = config.getProviders();
            int enabledProviderCount = 0;
            for (Map.Entry<String, VideoTranslateConfig.ProviderConfig> entry : providers.entrySet()) {
                VideoTranslateConfig.ProviderConfig providerConfig = entry.getValue();
                if (providerConfig.isEnabled()) {
                    enabledProviderCount++;
                    log.debug("[{}] 服务商{}超时时间: {}分钟",
                             methodName, entry.getKey(), providerConfig.getTimeoutMinutes());
                }
            }

            log.debug("[{}] 启用的服务商数量: {}", methodName, enabledProviderCount);

            // 3. 处理超时任务
            long startTime = System.currentTimeMillis();
            videoTranslateAsyncService.processTimeoutTasks();
            long timeoutProcessTime = System.currentTimeMillis() - startTime;

            log.debug("[{}] 超时任务处理完成，耗时: {}ms", methodName, timeoutProcessTime);

        } catch (Exception e) {
            log.error("[{}] 处理超时任务异常: {}", methodName, e.getMessage(), e);
        }
    }

    /**
     * 定时同步正在处理中的任务状态
     * 使用处理器的状态同步方法
     * 执行频率：每1分钟执行一次
     *
     * 优化后的逻辑：
     * 1. 检查处理器健康状态
     * 2. 批量同步processing状态的任务
     * 3. 监控同步性能和异常
     * 4. 记录同步统计信息
     */
    @Scheduled(fixedRateString = "${digital.video.translate.scheduler.status-sync-rate:60000}")
    public void syncProcessingTasksStatus() {
        String methodName = "syncProcessingTasksStatus";
        try {
            // 检查统一任务系统配置 - 状态同步在兜底模式下仍需执行
            if (useUniversalTask && !fallbackEnabled) {
                log.debug("[{}] 统一任务系统已启用且兜底机制禁用，跳过状态同步", methodName);
                return;
            }

            log.debug("[{}] 开始同步处理中任务状态...", methodName);

            // 1. 检查处理器可用性
            List<VideoTranslateProcessor> healthyProcessors = processorFactory.getHealthyProcessors();
            if (healthyProcessors.isEmpty()) {
                log.warn("[{}] 没有健康的处理器可用于状态同步", methodName);
                return;
            }

            // 2. 记录轮询配置
            long pollInterval = config.getPollIntervalMs();
            log.debug("[{}] 状态同步轮询间隔: {}毫秒, 健康处理器数量: {}",
                     methodName, pollInterval, healthyProcessors.size());

            // 3. 获取所有处理中的任务并批量同步状态
            long startTime = System.currentTimeMillis();
            videoTranslateAsyncService.syncProcessingTasksStatus();
            long syncTime = System.currentTimeMillis() - startTime;

            log.debug("[{}] 状态同步完成，耗时: {}ms", methodName, syncTime);

        } catch (Exception e) {
            log.error("[{}] 同步处理中任务状态异常: {}", methodName, e.getMessage(), e);
        }
    }

    /**
     * 定时清理过期的任务记录
     * 执行频率：每天凌晨3点执行
     */
    // @Scheduled(cron = "${video.translate.scheduler.cleanup-cron:0 0 3 * * ?}")
    public void cleanupExpiredTasks() {
        try {
            log.info("开始清理过期的视频翻译任务记录...");
            
            // 清理逻辑：删除30天前的已完成/失败/取消的任务记录
            // 这个方法将在VideoTranslateAsyncService中实现
            // videoTranslateAsyncService.cleanupExpiredTasks(30);
            
            log.info("过期任务记录清理完成");
            
        } catch (Exception e) {
            log.error("清理过期任务记录异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 定时生成任务统计报告
     * 包含处理器统计和监控信息
     * 执行频率：每天早上8点执行
     */
    // @Scheduled(cron = "${digital.video.translate.scheduler.statistics-cron:0 0 8 * * ?}")
    public void generateTaskStatistics() {
        try {
            log.info("开始生成视频翻译任务统计报告...");

            // 生成处理器统计
            generateProcessorStatistics();

            // 生成任务统计
            var statisticsResult = videoTranslateAsyncService.getTaskStatistics(null);
            if (statisticsResult.isSuccess()) {
                var statistics = statisticsResult.getData();

                log.info("视频翻译任务统计报告: " +
                        "总任务数={}, 成功率={}%, 今日任务数={}",
                        statistics.get("totalCount"),
                        statistics.get("successRate"),
                        statistics.get("todayCount"));

                // 这里可以添加更多的统计信息处理，比如：
                // 1. 发送统计报告到管理员邮箱
                // 2. 推送到监控系统
                // 3. 存储到统计数据库表

            } else {
                log.error("生成任务统计报告失败: {}", statisticsResult.getMessage());
            }

        } catch (Exception e) {
            log.error("生成任务统计报告异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 定时检查处理器健康状态
     * 执行频率：每3分钟执行一次
     */
    @Scheduled(fixedRateString = "${digital.video.translate.scheduler.processor-health-check-rate:180000}")
    public void checkProcessorHealthStatus() {
        try {
            log.debug("开始检查处理器健康状态...");
            checkProcessorHealth();
        } catch (Exception e) {
            log.error("检查处理器健康状态异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 定时处理器故障转移检查
     * 执行频率：每10分钟执行一次
     */
    @Scheduled(fixedRateString = "${digital.video.translate.scheduler.failover-check-rate:600000}")
    public void checkProcessorFailover() {
        try {
            log.debug("开始检查处理器故障转移状态...");

            // 检查是否启用故障转移
            if (!config.isFallbackEnabled()) {
                log.debug("故障转移功能未启用，跳过检查");
                return;
            }

            // 检查默认处理器状态
            String defaultProvider = config.getDefaultProvider();
            var defaultProcessor = processorFactory.getProcessor(defaultProvider);

            if (defaultProcessor.isEmpty() || !defaultProcessor.get().isHealthy()) {
                log.warn("默认处理器{}不可用，检查备用处理器", defaultProvider);

                List<VideoTranslateProcessor> fallbackProcessors = processorFactory.getFallbackProcessors(defaultProvider);
                if (fallbackProcessors.isEmpty()) {
                    log.error("没有可用的备用处理器，系统可能无法正常工作");
                } else {
                    log.info("发现{}个可用的备用处理器: {}",
                        fallbackProcessors.size(),
                        fallbackProcessors.stream().map(VideoTranslateProcessor::getProviderName).toList());
                }
            }

        } catch (Exception e) {
            log.error("检查处理器故障转移异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 检查处理器健康状态的私有方法
     */
    private void checkProcessorHealth() {
        try {
            // 获取所有处理器
            List<VideoTranslateProcessor> allProcessors = processorFactory.getSortedProcessors();
            List<VideoTranslateProcessor> healthyProcessors = processorFactory.getHealthyProcessors();

            log.debug("处理器健康检查 - 总数: {}, 健康: {}", allProcessors.size(), healthyProcessors.size());

            // 检查每个处理器的健康状态
            for (VideoTranslateProcessor processor : allProcessors) {
                String providerName = processor.getProviderName();
                boolean isEnabled = config.isProviderEnabled(providerName);
                boolean isHealthy = processor.isHealthy();

                if (isEnabled && !isHealthy) {
                    log.warn("处理器{}已启用但健康检查失败: {}", providerName, processor.getDescription());
                } else if (isEnabled && isHealthy) {
                    log.debug("处理器{}健康状态正常", providerName);
                }
            }

            // 检查是否有足够的健康处理器
            if (healthyProcessors.isEmpty()) {
                log.error("没有健康的处理器可用，系统无法处理新任务");
            } else if (healthyProcessors.size() == 1) {
                log.warn("只有一个健康的处理器可用，建议检查其他处理器状态");
            }

        } catch (Exception e) {
            log.error("检查处理器健康状态时发生异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 生成处理器统计信息的私有方法
     */
    private void generateProcessorStatistics() {
        try {
            log.info("=== 处理器统计报告 ===");

            // 获取处理器统计信息
            Map<String, Object> statistics = processorFactory.getStatistics();

            log.info("处理器总数: {}", statistics.get("totalProcessors"));
            log.info("健康处理器数: {}", statistics.get("healthyProcessors"));
            log.info("启用处理器数: {}", statistics.get("enabledProcessors"));

            // 详细的处理器状态
            Set<String> supportedProviders = processorFactory.getSupportedProviders();
            for (String provider : supportedProviders) {
                var processor = processorFactory.getProcessor(provider);
                if (processor.isPresent()) {
                    VideoTranslateProcessor p = processor.get();
                    boolean enabled = config.isProviderEnabled(provider);
                    boolean healthy = p.isHealthy();
                    int priority = p.getPriority();

                    log.info("处理器: {} | 启用: {} | 健康: {} | 优先级: {} | 描述: {}",
                        provider, enabled ? "是" : "否", healthy ? "是" : "否", priority, p.getDescription());
                }
            }

            log.info("=== 处理器统计报告结束 ===");

        } catch (Exception e) {
            log.error("生成处理器统计信息时发生异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 兜底机制：处理可能丢失的任务
     * 查找超过5分钟仍在排队状态的任务，重新提交到统一任务系统
     */
    private void processFailoverTasks() {
        String methodName = "processFailoverTasks";
        try {
            log.debug("[{}] 开始执行兜底机制，查找可能丢失的任务...", methodName);

            // 查找超过5分钟仍在排队状态的任务
            LocalDateTime staleThreshold = LocalDateTime.now().minusMinutes(5);

            List<VideoTranslateTaskPO> staleTasks = videoTranslateTaskMapper.selectList(
                new LambdaQueryWrapper<VideoTranslateTaskPO>()
                    .eq(VideoTranslateTaskPO::getStatus, 2) // 排队中状态 (QUEUING)
                    .lt(VideoTranslateTaskPO::getUpdateTime, staleThreshold)
                    .eq(VideoTranslateTaskPO::getIsDeleted, 0)
                    .last("LIMIT 20") // 批量处理，避免一次处理过多
            );

            if (staleTasks.isEmpty()) {
                log.debug("[{}] 未发现需要兜底处理的任务", methodName);
                return;
            }

            log.info("[{}] 发现{}个可能丢失的任务，开始兜底处理", methodName, staleTasks.size());

            int rescuedCount = 0;
            for (VideoTranslateTaskPO task : staleTasks) {
                try {
                    // 重新提交到原有的处理流程
                    // 这里调用原有的处理逻辑，确保任务能被重新处理
                    log.info("[{}] 兜底处理任务: taskId={}, userId={}, 排队时间={}分钟",
                            methodName, task.getTaskId(), task.getUserId(),
                            java.time.Duration.between(task.getUpdateTime(), LocalDateTime.now()).toMinutes());

                    // 更新任务的更新时间，标记为已被兜底机制处理
                    task.setUpdateTime(LocalDateTime.now());
                    videoTranslateTaskMapper.updateById(task);

                    // 重新触发任务处理
                    videoTranslateAsyncService.processQueueingTasks();

                    rescuedCount++;

                } catch (Exception e) {
                    log.error("[{}] 兜底处理任务失败: taskId={}, error={}",
                            methodName, task.getTaskId(), e.getMessage(), e);
                }
            }

            log.info("[{}] 兜底机制执行完成，成功处理{}个任务", methodName, rescuedCount);

        } catch (Exception e) {
            log.error("[{}] 兜底机制执行异常: {}", methodName, e.getMessage(), e);
        }
    }

}
