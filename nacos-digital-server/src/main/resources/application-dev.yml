server:
  port: 8818
  tomcat:
    uri-encoding: utf-8
    connection-timeout: 900000 # 设置为 60 秒（单位：毫秒）
    keep-alive-timeout: 900000 # 设置为 60 秒（单位：毫秒）
spring:
  mvc:
    servlet:
      path: /digital
  application:
    name: nacos-digital-server
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    url: ************************************************************************************************************************************************************************************************************************************************************
    username: ddsjtest
    password: R4egP0btnwGz3y
    driver-class-name: com.mysql.cj.jdbc.Driver
    tomcat:
      max-active: 30
      initial-size: 1
      max-wait: 60000
      min-idle: 2
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 25200000
      validation-query: select '1'
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false

  #cloud配置
  cloud:
    inetutils:
      preferred-networks: 192.168.3
    nacos:
      discovery: # 服务发现
        enabled: true
        server-addr: *************:8848 # Nacos 服务地址
        group: DEFAULT_GROUP # 组名
        namespace: fc9ece80-4211-4855-bc15-25bf1db13a8a # 命名空间
        username: nacos # Nacos 用户名
        password: nacos # Nacos 密码
        auth:
          enabled: true
          system:
            type: nacos
      config: # 配置中心
        enabled: true
        server-addr: *************:8848
        file-extension: yml
        namespace: fc9ece80-4211-4855-bc15-25bf1db13a8a
        username: nacos # Nacos 用户名
        password: nacos # Nacos 密码
  # redis服务连接
  data:
    redis:
      host: *************
      port: 9979
      database: 13
      password: abc123
      lettuce:
        pool:
          # 连接池最大连接数（负值表示没有限制）
          max-active: 200
          # 连接池最大空闲连接数
          max-idle: 50
          # 连接池最小空闲连接数
          min-idle: 10
          # 连接池最大阻塞等待时间（负值表示没有限制）
          max-wait: 5000ms
        # 连接超时时间
        timeout: 10000ms
        # 命令超时时间
        command-timeout: 5000ms
        # 关闭超时时间
        shutdown-timeout: 100ms
        cluster:
          refresh:
            adaptive: true
  # config 配置
  config:
    import:
      - optional:nacos:${spring.application.name}-dev.${spring.cloud.nacos.config.file-extension}

logs:
  path: ./work/logs

changjin_secret_key: ee0cf34fb2c74abd882b0f26052bd094

# ==================== 开发环境 - 统一任务系统配置 ====================
universal-task:
  # 开发环境视频翻译配置
  video-translate:
    enabled: true                            # 开发环境启用统一任务系统测试
    fallback-enabled: true                   # 保持兜底机制

  # 开发环境处理器配置
  processors:
    video-translate:
      enabled: true
      timeout-minutes: 10                    # 开发环境缩短超时时间
      max-concurrent: 2                      # 开发环境降低并发数
      retry-attempts: 2                      # 开发环境减少重试次数

  # 开发环境启用详细日志
  monitoring:
    logging:
      level: "DEBUG"                          # 开发环境使用DEBUG级别
      include-context: true                   # 包含详细上下文信息
      include-stack-trace: true               # 包含异常堆栈信息

  # 开发环境缩短超时时间便于测试
  timeout:
    default: 300000                          # 开发环境默认5分钟超时
    video-translate: 600000                  # 视频翻译10分钟超时
    audio-generate: 180000                   # 音频生成3分钟超时
    video-edit: 900000                       # 视频编辑15分钟超时

  # 开发环境加快兜底调度频率
  fallback-scheduler:
    fixed-delay: 30000                       # 30秒执行一次
    initial-delay: 10000                     # 10秒后开始执行
    batch-size: 20                          # 每次处理20个任务

  # 开发环境启用所有监控功能
  monitoring:
    metrics:
      enabled: true
      export-interval: 30000                 # 30秒导出一次指标
    logging:
      enabled: true
      level: "DEBUG"
