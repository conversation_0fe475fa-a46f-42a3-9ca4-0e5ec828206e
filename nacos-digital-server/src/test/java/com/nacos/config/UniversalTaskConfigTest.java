package com.nacos.config;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 统一任务系统配置测试
 * 验证配置类的基本功能
 */
public class UniversalTaskConfigTest {

    @Test
    public void testConfigurationDefaults() {
        // 创建配置对象并验证默认值
        UniversalTaskConfig config = new UniversalTaskConfig();

        // 验证基本配置
        assertNotNull(config.getEnabled(), "enabled配置不能为空");
        assertTrue(config.getEnabled(), "主配置应该启用");

        // 验证视频翻译配置
        assertNotNull(config.getVideoTranslate(), "视频翻译配置不能为空");
        assertFalse(config.getVideoTranslate().getEnabled(), "默认应该禁用视频翻译统一系统");
        assertTrue(config.getVideoTranslate().getFallbackEnabled(), "兜底机制应该启用");

        // 验证处理器配置
        assertNotNull(config.getProcessors(), "处理器配置不能为空");
        assertNotNull(config.getProcessors().getVideoTranslate(), "视频翻译处理器配置不能为空");
        assertTrue(config.getProcessors().getVideoTranslate().getEnabled(), "视频翻译处理器应该启用");
        assertEquals(30, config.getProcessors().getVideoTranslate().getTimeoutMinutes(), "默认超时时间应该是30分钟");
        assertEquals(5, config.getProcessors().getVideoTranslate().getMaxConcurrent(), "默认最大并发数应该是5");
        assertEquals(3, config.getProcessors().getVideoTranslate().getRetryAttempts(), "默认重试次数应该是3");

        // 验证Redis配置
        assertNotNull(config.getRedis(), "Redis配置不能为空");
        assertNotNull(config.getRedis().getQueues(), "队列配置不能为空");
        assertEquals("TOPIC_VIDEO_TRANSLATE", config.getRedis().getQueues().getVideoTranslate(), "视频翻译队列名称应该正确");

        // 验证兜底调度器配置
        assertNotNull(config.getFallbackScheduler(), "兜底调度器配置不能为空");
        assertTrue(config.getFallbackScheduler().getEnabled(), "兜底调度器应该启用");
        assertEquals(60000L, config.getFallbackScheduler().getFixedDelay(), "默认执行间隔应该是60秒");
        assertEquals(30000L, config.getFallbackScheduler().getInitialDelay(), "默认初始延迟应该是30秒");
        assertEquals(50, config.getFallbackScheduler().getBatchSize(), "默认批量大小应该是50");

        // 验证监控配置
        assertNotNull(config.getMonitoring(), "监控配置不能为空");
        assertNotNull(config.getMonitoring().getLogging(), "日志配置不能为空");
        assertEquals("INFO", config.getMonitoring().getLogging().getLevel(), "默认日志级别应该是INFO");
        assertTrue(config.getMonitoring().getLogging().getIncludeContext(), "应该包含上下文信息");
        assertFalse(config.getMonitoring().getLogging().getIncludeStackTrace(), "默认不应该包含堆栈信息");

        // 验证超时配置
        assertNotNull(config.getTimeout(), "超时配置不能为空");
        assertEquals(1800000L, config.getTimeout().getDefaultTimeout(), "默认超时应该是30分钟");
        assertEquals(1800000L, config.getTimeout().getVideoTranslate(), "默认视频翻译超时应该是30分钟");
    }

    @Test
    public void testBusinessMethods() {
        UniversalTaskConfig config = new UniversalTaskConfig();

        // 测试获取队列名称
        assertEquals("TOPIC_VIDEO_TRANSLATE", config.getQueueName("VIDEO_TRANSLATE"));
        assertEquals("TOPIC_AUDIO_GENERATE", config.getQueueName("AUDIO_GENERATE"));
        assertEquals("TOPIC_VIDEO_EDIT", config.getQueueName("VIDEO_EDIT"));

        // 测试不支持的任务类型
        assertThrows(IllegalArgumentException.class, () -> {
            config.getQueueName("UNKNOWN_TYPE");
        });

        // 测试获取超时时间
        assertEquals(1800000L, config.getTimeout("VIDEO_TRANSLATE"));
        assertEquals(300000L, config.getTimeout("AUDIO_GENERATE"));
        assertEquals(3600000L, config.getTimeout("VIDEO_EDIT"));
        assertEquals(1800000L, config.getTimeout("UNKNOWN_TYPE")); // 返回默认值
    }

    @Test
    public void testConfigValidation() {
        UniversalTaskConfig config = new UniversalTaskConfig();

        // 验证配置完整性
        assertTrue(config.isValid(), "配置应该是有效的");
    }
}
