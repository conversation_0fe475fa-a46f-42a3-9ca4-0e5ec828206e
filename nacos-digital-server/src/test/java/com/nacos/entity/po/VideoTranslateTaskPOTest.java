package com.nacos.entity.po;

import com.nacos.entity.enums.UnifiedTaskStatusEnum;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import static org.junit.jupiter.api.Assertions.*;

/**
 * VideoTranslateTaskPO 状态迁移测试
 * 验证新的统一状态枚举是否正确工作
 * 
 * <AUTHOR>
 * @since 2025-08-01
 */
class VideoTranslateTaskPOTest {

    @Test
    @DisplayName("测试排队中状态")
    void testQueuingStatus() {
        VideoTranslateTaskPO task = new VideoTranslateTaskPO();
        task.setStatus(UnifiedTaskStatusEnum.QUEUING.getCode()); // 2
        
        assertTrue(task.isProcessing());
        assertFalse(task.isCompleted());
        assertFalse(task.isFailed());
        assertFalse(task.isCancelled());
        assertFalse(task.isFinalStatus());
        assertEquals("排队中", task.getStatusDescription());
        assertEquals(UnifiedTaskStatusEnum.QUEUING, task.getUnifiedStatus());
    }

    @Test
    @DisplayName("测试进行中状态")
    void testProgressStatus() {
        VideoTranslateTaskPO task = new VideoTranslateTaskPO();
        task.setStatus(UnifiedTaskStatusEnum.PROGRESS.getCode()); // 3
        
        assertTrue(task.isProcessing());
        assertFalse(task.isCompleted());
        assertFalse(task.isFailed());
        assertFalse(task.isCancelled());
        assertFalse(task.isFinalStatus());
        assertEquals("进行中", task.getStatusDescription());
        assertEquals(UnifiedTaskStatusEnum.PROGRESS, task.getUnifiedStatus());
    }

    @Test
    @DisplayName("测试成功状态")
    void testSuccessStatus() {
        VideoTranslateTaskPO task = new VideoTranslateTaskPO();
        task.setStatus(UnifiedTaskStatusEnum.SUCCESS.getCode()); // 1
        
        assertFalse(task.isProcessing());
        assertTrue(task.isCompleted());
        assertFalse(task.isFailed());
        assertFalse(task.isCancelled());
        assertTrue(task.isFinalStatus());
        assertEquals("翻译成功", task.getStatusDescription());
        assertEquals(UnifiedTaskStatusEnum.SUCCESS, task.getUnifiedStatus());
    }

    @Test
    @DisplayName("测试失败状态")
    void testFailedStatus() {
        VideoTranslateTaskPO task = new VideoTranslateTaskPO();
        task.setStatus(UnifiedTaskStatusEnum.FAILED.getCode()); // 0
        
        assertFalse(task.isProcessing());
        assertFalse(task.isCompleted());
        assertTrue(task.isFailed());
        assertFalse(task.isCancelled());
        assertTrue(task.isFinalStatus());
        assertEquals("失败", task.getStatusDescription());
        assertEquals(UnifiedTaskStatusEnum.FAILED, task.getUnifiedStatus());
    }

    @Test
    @DisplayName("测试超时状态")
    void testTimeoutStatus() {
        VideoTranslateTaskPO task = new VideoTranslateTaskPO();
        task.setStatus(UnifiedTaskStatusEnum.TIMEOUT.getCode()); // 4
        
        assertFalse(task.isProcessing());
        assertFalse(task.isCompleted());
        assertTrue(task.isFailed());
        assertFalse(task.isCancelled());
        assertTrue(task.isFinalStatus());
        assertEquals("超时", task.getStatusDescription());
        assertEquals(UnifiedTaskStatusEnum.TIMEOUT, task.getUnifiedStatus());
    }

    @Test
    @DisplayName("测试已取消状态")
    void testCancelledStatus() {
        VideoTranslateTaskPO task = new VideoTranslateTaskPO();
        task.setStatus(UnifiedTaskStatusEnum.CANCELLED.getCode()); // 5
        
        assertFalse(task.isProcessing());
        assertFalse(task.isCompleted());
        assertFalse(task.isFailed());
        assertTrue(task.isCancelled());
        assertTrue(task.isFinalStatus());
        assertEquals("已取消", task.getStatusDescription());
        assertEquals(UnifiedTaskStatusEnum.CANCELLED, task.getUnifiedStatus());
    }

    @Test
    @DisplayName("测试统一状态枚举便捷方法")
    void testUnifiedStatusMethods() {
        VideoTranslateTaskPO task = new VideoTranslateTaskPO();
        
        // 测试设置统一状态
        task.setUnifiedStatus(UnifiedTaskStatusEnum.QUEUING);
        assertEquals(UnifiedTaskStatusEnum.QUEUING.getCode(), task.getStatus());
        assertEquals(UnifiedTaskStatusEnum.QUEUING, task.getUnifiedStatus());
        
        // 测试状态转换检查
        assertTrue(task.canTransitionTo(UnifiedTaskStatusEnum.PROGRESS));
        assertTrue(task.canTransitionTo(UnifiedTaskStatusEnum.FAILED));
        assertTrue(task.canTransitionTo(UnifiedTaskStatusEnum.CANCELLED));
        assertFalse(task.canTransitionTo(UnifiedTaskStatusEnum.SUCCESS)); // 不能直接从排队转为成功
        
        // 测试显示样式
        assertEquals("processing", task.getStatusDisplayStyle());
        
        // 切换到成功状态
        task.setUnifiedStatus(UnifiedTaskStatusEnum.SUCCESS);
        assertEquals("success", task.getStatusDisplayStyle());
        
        // 最终状态不能再转换
        assertFalse(task.canTransitionTo(UnifiedTaskStatusEnum.FAILED));
    }

    @Test
    @DisplayName("测试状态转换规则")
    void testStatusTransitionRules() {
        VideoTranslateTaskPO task = new VideoTranslateTaskPO();
        
        // 排队中 -> 进行中
        task.setUnifiedStatus(UnifiedTaskStatusEnum.QUEUING);
        assertTrue(task.canTransitionTo(UnifiedTaskStatusEnum.PROGRESS));
        
        // 进行中 -> 成功
        task.setUnifiedStatus(UnifiedTaskStatusEnum.PROGRESS);
        assertTrue(task.canTransitionTo(UnifiedTaskStatusEnum.SUCCESS));
        assertTrue(task.canTransitionTo(UnifiedTaskStatusEnum.FAILED));
        assertTrue(task.canTransitionTo(UnifiedTaskStatusEnum.TIMEOUT));
        assertTrue(task.canTransitionTo(UnifiedTaskStatusEnum.CANCELLED));
        
        // 成功状态不能再转换
        task.setUnifiedStatus(UnifiedTaskStatusEnum.SUCCESS);
        assertFalse(task.canTransitionTo(UnifiedTaskStatusEnum.FAILED));
        assertFalse(task.canTransitionTo(UnifiedTaskStatusEnum.PROGRESS));
    }

    @Test
    @DisplayName("测试空状态处理")
    void testNullStatus() {
        VideoTranslateTaskPO task = new VideoTranslateTaskPO();
        task.setStatus(null);
        
        assertFalse(task.isProcessing());
        assertFalse(task.isCompleted());
        assertFalse(task.isFailed());
        assertFalse(task.isCancelled());
        assertFalse(task.isFinalStatus());
        assertEquals("未知状态", task.getStatusDescription());
        assertNull(task.getUnifiedStatus());
        assertEquals("default", task.getStatusDisplayStyle());
    }
}
